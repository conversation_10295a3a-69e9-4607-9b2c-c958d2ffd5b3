# Vue3 + Tailwind CSS 4 管理后台迁移设计方案

## 项目概述

将现有的基于 LayUI 的管理后台系统迁移到 Vue 3 + Tailwind CSS 4 技术栈，保留原有功能和布局，优化用户体验和代码结构。

## 技术栈选择

- **前端框架**: Vue 3 + Composition API (`<script setup>`)
- **CSS框架**: Tailwind CSS 4
- **构建工具**: Vite
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **图表库**: ECharts
- **图标**: 保留原有 iconfont 图标

## 项目结构

```
vue-admin/
├── src/
│   ├── components/           # 公共组件
│   │   ├── layout/          # 布局组件
│   │   │   ├── AppHeader.vue      # 顶部导航栏
│   │   │   ├── AppSidebar.vue     # 左侧边栏
│   │   │   ├── AppLayout.vue      # 主布局
│   │   │   └── ThemeToggle.vue    # 主题切换
│   │   ├── ui/              # UI组件
│   │   │   ├── DataTable.vue      # 数据表格组件
│   │   │   ├── SearchForm.vue     # 搜索表单组件
│   │   │   ├── StatCard.vue       # 统计卡片组件
│   │   │   ├── ChartContainer.vue # 图表容器组件
│   │   │   └── Pagination.vue     # 分页组件
│   │   └── icons/           # 图标组件
│   │       └── IconFont.vue       # 图标字体组件
│   ├── views/               # 页面组件
│   │   ├── Dashboard.vue          # 仪表盘
│   │   ├── SubAccountManage.vue   # 子账号管理
│   │   ├── StoreManage.vue        # 店铺管理
│   │   ├── WorkOrderPanel.vue     # 工单面板
│   │   └── AfterSalesOrder.vue    # 售后工单
│   ├── composables/         # 组合式函数
│   │   ├── useTheme.js           # 主题管理
│   │   ├── useTable.js           # 表格数据管理
│   │   └── useCharts.js          # 图表管理
│   ├── assets/              # 静态资源
│   │   ├── images/
│   │   └── styles/
│   ├── router/              # 路由配置
│   │   └── index.js
│   ├── stores/              # 状态管理
│   │   └── user.js
│   ├── utils/               # 工具函数
│   │   └── api.js
│   ├── App.vue
│   └── main.js
├── public/
├── package.json
├── vite.config.js
├── tailwind.config.js
└── index.html
```

## 页面分析与设计

### 1. 仪表盘 (Dashboard.vue)

**功能模块:**
- 统计卡片区域（4个卡片）
- 问题分析表图表
- 用量TOP3列表
- 近8小时用量分析图表
- 最近消息表格

**组件构成:**
- StatCard.vue × 4
- ChartContainer.vue × 2
- DataTable.vue × 1

### 2. 子账号管理 (SubAccountManage.vue)

**功能模块:**
- 搜索筛选表单
- 子账号数据表格
- 分页组件
- 右键菜单操作

**表格字段:**
- 账号、会员等级、余额、会员到期、账号状态、登陆时间、是否在线、店铺数量、店铺在线数量

### 3. 店铺管理 (StoreManage.vue)

**功能模块:**
- 搜索筛选表单
- 店铺数据表格
- 分页组件

**表格字段:**
- 店铺名称、今日接待、消耗点数、进线次数、转接次数、所属账号、入库时间、是否在线

### 4. 工单面板 (WorkOrderPanel.vue)

**功能模块:**
- 搜索筛选表单
- 工单数据表格
- 分页组件

**表格字段:**
- 事件类型、商品名称、对方昵称/订单号、快递单号/物流状态、操作

### 5. 售后工单 (AfterSalesOrder.vue)

**功能模块:**
- 搜索筛选表单
- 售后工单数据表格
- 分页组件

**表格字段:**
- 售后编号、商品信息、金额、售后申请时间、售后类型、申请原因、快递信息、操作

## 组件设计详情

### 布局组件 (4个)

#### AppLayout.vue
- 主布局容器
- 响应式布局
- 侧边栏折叠状态管理

#### AppHeader.vue
- Logo展示
- 侧边栏折叠按钮
- 主题切换按钮
- 其他工具按钮

#### AppSidebar.vue
- 导航菜单
- 消息区域
- 用户信息展示
- 点数余额显示

#### ThemeToggle.vue
- 明暗主题切换
- 本地存储主题设置

### UI组件 (5个)

#### StatCard.vue
- 统计数据展示
- 图标 + 标题 + 数值
- 支持不同颜色主题

#### DataTable.vue
- 通用数据表格
- 支持排序、筛选
- 响应式设计
- 自定义列配置

#### SearchForm.vue
- 通用搜索表单
- 动态表单项配置
- 搜索和重置功能

#### ChartContainer.vue
- ECharts图表封装
- 响应式图表
- 主题适配

#### Pagination.vue
- 分页组件
- 页码跳转
- 每页条数设置

### 图标组件 (1个)

#### IconFont.vue
- 图标字体封装
- 支持原有iconfont图标

## 数据结构设计

### 统计卡片数据
```javascript
const statsData = [
  { title: '子账号管理', value: 524, icon: 'user', color: 'blue' },
  { title: '店铺数量', value: 524, icon: 'store', color: 'green' },
  { title: '今日通知次数', value: 524, icon: 'notification', color: 'purple' },
  { title: '问题数量', value: '99/120', icon: 'warning', color: 'orange' }
]
```

### 子账号数据
```javascript
const subAccounts = [
  { 
    account: '184762', 
    level: 0, 
    balance: 999.00, 
    expireTime: '2025/7/5 21:04:34',
    status: '禁用', // 或 '正常'
    loginTime: '登陆时间',
    online: '离线',
    storeCount: 1,
    onlineStoreCount: 0
  }
]
```

### 店铺数据
```javascript
const stores = [
  {
    name: '科沃斯',
    todayReception: 0,
    pointsConsumed: 0,
    incomingCount: 0,
    transferCount: 0,
    account: '10086',
    createTime: '2025/7/5 21:04:34',
    online: '离线'
  }
]
```

## 样式设计原则

### Tailwind CSS 4 使用规范
- 使用 `@import "tailwindcss"` 导入
- 利用 Tailwind 的响应式设计类
- 自定义主题变量
- 组件级别的样式封装

### 主题设计
- 支持明暗两种主题
- 使用 CSS 变量实现主题切换
- 保持与原设计风格一致

### 响应式设计
- 移动端适配
- 平板端适配
- 桌面端优化

## 路由设计

```javascript
const routes = [
  { path: '/', redirect: '/dashboard' },
  { path: '/dashboard', component: Dashboard, name: 'Dashboard' },
  { path: '/sub-account', component: SubAccountManage, name: 'SubAccount' },
  { path: '/store', component: StoreManage, name: 'Store' },
  { path: '/work-order', component: WorkOrderPanel, name: 'WorkOrder' },
  { path: '/after-sales', component: AfterSalesOrder, name: 'AfterSales' }
]
```

## 状态管理设计

### 用户状态 (user.js)
- 用户信息
- 登录状态
- 权限管理

### 主题状态 (theme.js)
- 当前主题
- 主题切换逻辑

### 应用状态 (app.js)
- 侧边栏折叠状态
- 全局加载状态

## 开发规范

### 代码规范
- 使用 Vue 3 Composition API
- 优先使用 `<script setup>` 语法
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

### 提交规范
- feat: 新功能
- fix: 修复bug
- style: 样式调整
- refactor: 重构代码

## 性能优化

### 代码分割
- 路由级别的懒加载
- 组件级别的异步加载

### 资源优化
- 图片压缩和懒加载
- 字体文件优化
- CSS 和 JS 压缩

### 缓存策略
- 静态资源缓存
- API 数据缓存
- 路由缓存

## 兼容性考虑

### 浏览器支持
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 移动端支持
- iOS Safari 14+
- Android Chrome 90+

## 部署方案

### 构建配置
- Vite 生产环境构建
- 资源压缩和优化
- 环境变量配置

### 部署流程
- 自动化构建
- 静态资源部署
- CDN 配置
