# 智客AI管理后台 - 全局变量接口文档

## 概述
本文档说明如何通过客户端JS代码与Vue管理后台进行数据交互。系统提供了全局变量接口，允许客户端直接修改表格数据、分页信息等。

## 全局变量命名规范

### 侧边栏用户信息
- `window.sidebar_userInfo` - 用户信息数据
- `window.sidebar_messageCount` - 消息数量数据

### 仪表盘页面
- `window.dashboard_stats` - 统计卡片数据
- `window.dashboard_topUsage` - 用量TOP3数据数组
- `window.dashboard_recentMessages` - 最近消息数据数组
- `window.dashboard_chartData` - 图表数据

### 子账号管理页面
- `window.table_zizhanghao` - 表格数据数组
- `window.table_zizhanghao_currentPage` - 当前页码
- `window.table_zizhanghao_pageSize` - 每页显示条数
- `window.table_zizhanghao_total` - 总记录数

### 店铺管理页面
- `window.table_dianpu` - 表格数据数组
- `window.table_dianpu_currentPage` - 当前页码
- `window.table_dianpu_pageSize` - 每页显示条数
- `window.table_dianpu_total` - 总记录数

### 工单面板页面
- `window.table_gongdan` - 表格数据数组
- `window.table_gongdan_currentPage` - 当前页码
- `window.table_gongdan_pageSize` - 每页显示条数
- `window.table_gongdan_total` - 总记录数

### 售后工单页面
- `window.table_shouhou` - 表格数据数组
- `window.table_shouhou_currentPage` - 当前页码
- `window.table_shouhou_pageSize` - 每页显示条数
- `window.table_shouhou_total` - 总记录数

## 数据结构示例

### 侧边栏用户信息数据结构
```javascript
window.sidebar_userInfo = {
  avatar: '张',              // 用户头像（显示的字符）
  name: '张三',              // 用户名称
  position: '管理员',         // 用户职位
  balance: '0.00'           // 点数余额
}
```

### 侧边栏消息数量数据结构
```javascript
window.sidebar_messageCount = {
  inbox: 5,                 // 收件箱消息数量
  notification: 3           // 通知消息数量
}
```

### 仪表盘数据结构

#### 统计卡片数据
```javascript
window.dashboard_stats = {
  subAccountCount: 524,           // 子账号管理数量
  storeCount: 524,               // 店铺数量
  todayNotifications: 524,        // 今日通知次数
  problemCount: {                // 问题数量
    current: 99,                 // 当前问题数
    total: 120                   // 总问题数
  }
}
```

#### 用量TOP3数据
```javascript
window.dashboard_topUsage = [
  {
    name: '是是是',               // 用户名称
    usage: 123,                  // 用量数值
    avatar: '/img/logo.jpg'      // 头像路径
  }
  // ... 更多数据
]
```

#### 最近消息数据
```javascript
window.dashboard_recentMessages = [
  {
    store: '无忧',               // 店铺名称
    user: '是是是',              // 用户名称
    content: '我买的裤子质里很好...', // 消息内容
    reply: '亲亲这款是标准长裤哦',    // 回复内容
    time: '2025/6/17 17:36:33'   // 时间
  }
  // ... 更多数据
]
```

#### 图表数据
```javascript
window.dashboard_chartData = {
  problemChart: {                // 问题分析图表
    xData: ['07-06', '07-07', '07-08', '07-09', '07-10', '07-11', '今天'],
    workOrderData: [100, 200, 150, 250, 200, 300, 150],  // 工单数量
    afterSalesData: [50, 80, 70, 100, 90, 120, 80]       // 售后数量
  },
  usageChart: {                  // 用量分析图表
    xData: ['18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00'],
    yData: [120, 200, 150, 80, 70, 110, 130, 180]        // 用量数据
  }
}
```

### 子账号管理数据结构
```javascript
window.table_zizhanghao = [
  {
    account: '184762',           // 账号
    level: 0,                   // 会员等级
    balance: '999.00',          // 余额
    expireTime: '2025/7/5 21:04:34',  // 会员到期时间
    status: '正常',             // 账号状态：'正常' | '禁用'
    loginTime: '2025/7/5 21:04:34',   // 登录时间
    online: '在线',             // 是否在线：'在线' | '离线'
    storeCount: 1,              // 店铺数量
    onlineStoreCount: 0         // 店铺在线数量
  }
  // ... 更多数据
]
```

### 店铺管理数据结构
```javascript
window.table_dianpu = [
  {
    name: '科沃斯',             // 店铺名称
    todayReception: 0,          // 今日接待
    pointsConsumed: 0,          // 消耗点数
    incomingCount: 0,           // 进线次数
    transferCount: 0,           // 转接次数
    account: '10086',           // 所属账号
    createTime: '2025/7/5 21:04:34',  // 入库时间
    online: '离线'              // 是否在线：'在线' | '离线'
  }
  // ... 更多数据
]
```

### 工单面板数据结构
```javascript
window.table_gongdan = [
  {
    eventType: '商品不满意',     // 事件类型
    eventColor: '#FF6868',      // 事件颜色
    platformIcon: '/img/pdd.png', // 平台图标
    storeName: '翼迅科技',      // 店铺名称
    productName: '科沃斯新品T80扫地机器人...', // 商品名称
    customerName: '我是一只小毛驴', // 客户昵称
    orderNumber: '694292551694292551', // 订单号
    trackingNumber: 'SF694294292551',  // 快递单号
    logisticsStatus: '已签收 2025-06-01', // 物流状态
    description: '买家认为商品是假货...'    // 描述
  }
  // ... 更多数据
]
```

### 售后工单数据结构
```javascript
window.table_shouhou = [
  {
    platformIcon: '/img/pdd.png',     // 平台图标
    orderNumber: '694292551694',      // 售后编号
    productInfo: {                    // 商品信息
      name: '科沃斯新品T80扫地机器人...',
      id: '123456789',
      spec: 't80 x1'
    },
    amount: {                         // 金额信息
      paid: '2999.00',               // 付款金额
      afterSales: '2999.00'          // 售后金额
    },
    applyTime: '2025-07-12 13:00:00', // 申请时间
    type: '商品不满意',               // 售后类型
    typeColor: '#FF6868',             // 类型颜色
    status: '待处理',                 // 状态
    reason: '原因内容...',            // 申请原因
    logistics: {                      // 快递信息
      company: '顺丰',
      number: 'SF694294292551',
      status: '已签收 2025-06-01'
    }
  }
  // ... 更多数据
]
```

## 客户端操作示例

### 1. 设置用户信息
```javascript
// 设置侧边栏用户信息
window.sidebar_userInfo = {
  avatar: '李',
  name: '李四',
  position: '超级管理员',
  balance: '9999.99'
}

// 刷新侧边栏显示
window.updateSidebarUserInfo()
```

### 2. 设置消息数量
```javascript
// 设置消息数量
window.sidebar_messageCount = {
  inbox: 10,              // 收件箱有10条消息
  notification: 5         // 通知有5条消息
}

// 刷新侧边栏显示
window.updateSidebarUserInfo()
```

### 3. 设置仪表盘数据
```javascript
// 设置统计卡片数据
window.dashboard_stats = {
  subAccountCount: 888,
  storeCount: 666,
  todayNotifications: 999,
  problemCount: { current: 150, total: 200 }
}

// 设置用量TOP3数据
window.dashboard_topUsage = [
  { name: '测试用户A', usage: 999, avatar: '/img/logo.jpg' },
  { name: '测试用户B', usage: 888, avatar: '/img/logo.jpg' },
  { name: '测试用户C', usage: 777, avatar: '/img/logo.jpg' }
]

// 设置最近消息数据
window.dashboard_recentMessages = [
  {
    store: '测试店铺',
    user: '测试客户',
    content: '这是一条测试消息内容',
    reply: '这是测试回复内容',
    time: '2025/7/22 12:00:00'
  }
]

// 设置图表数据
window.dashboard_chartData = {
  problemChart: {
    xData: ['测试1', '测试2', '测试3', '测试4', '测试5', '测试6', '今天'],
    workOrderData: [200, 300, 250, 350, 300, 400, 250],
    afterSalesData: [80, 120, 100, 150, 130, 180, 120]
  },
  usageChart: {
    xData: ['12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00'],
    yData: [200, 300, 250, 150, 120, 180, 220, 280]
  }
}

// 刷新仪表盘显示
window.updateDashboardData()
```

### 3. 设置表格数据
```javascript
// 设置子账号管理的表格数据
window.table_zizhanghao = [
  {
    account: '新账号001',
    level: 1,
    balance: '1500.00',
    expireTime: '2025/12/31 23:59:59',
    status: '正常',
    loginTime: '2025/7/22 10:30:00',
    online: '在线',
    storeCount: 3,
    onlineStoreCount: 2
  },
  {
    account: '新账号002',
    level: 0,
    balance: '500.00',
    expireTime: '2025/8/15 12:00:00',
    status: '禁用',
    loginTime: '2025/7/20 15:45:00',
    online: '离线',
    storeCount: 1,
    onlineStoreCount: 0
  }
]

// 设置分页信息
window.table_zizhanghao_currentPage = 1
window.table_zizhanghao_pageSize = 10
window.table_zizhanghao_total = 2
```

### 4. 添加新数据
```javascript
// 向现有数据中添加新记录
window.table_zizhanghao.push({
  account: '新增账号',
  level: 2,
  balance: '3000.00',
  expireTime: '2026/1/1 00:00:00',
  status: '正常',
  loginTime: '2025/7/22 11:00:00',
  online: '在线',
  storeCount: 5,
  onlineStoreCount: 4
})

// 更新总数
window.table_zizhanghao_total = window.table_zizhanghao.length
```

### 5. 修改现有数据
```javascript
// 修改第一条记录的状态
if (window.table_zizhanghao.length > 0) {
  window.table_zizhanghao[0].status = '禁用'
  window.table_zizhanghao[0].online = '离线'
}
```

### 6. 删除数据
```javascript
// 删除指定索引的记录
const indexToDelete = 1
if (window.table_zizhanghao.length > indexToDelete) {
  window.table_zizhanghao.splice(indexToDelete, 1)
  window.table_zizhanghao_total = window.table_zizhanghao.length
}
```

### 7. 清空数据
```javascript
// 清空所有数据
window.table_zizhanghao = []
window.table_zizhanghao_total = 0
window.table_zizhanghao_currentPage = 1
```

### 8. 分页操作
```javascript
// 跳转到指定页面
window.table_zizhanghao_currentPage = 3

// 修改每页显示条数
window.table_zizhanghao_pageSize = 20
```

### 9. 仪表盘图表数据更新
```javascript
// 更新问题分析图表
window.dashboard_chartData.problemChart = {
  xData: ['新数据1', '新数据2', '新数据3', '新数据4', '新数据5'],
  workOrderData: [300, 400, 350, 450, 400],
  afterSalesData: [120, 160, 140, 200, 180]
}

// 更新用量分析图表
window.dashboard_chartData.usageChart = {
  xData: ['20:00', '21:00', '22:00', '23:00', '00:00'],
  yData: [300, 400, 350, 250, 200]
}

// 刷新仪表盘显示
window.updateDashboardData()
```

## 数据同步机制

系统采用主动更新机制，修改全局变量后需要调用对应的更新函数来刷新界面：

### 更新函数
- `window.updateSidebarUserInfo()` - 刷新侧边栏用户信息
- `window.updateDashboardData()` - 刷新仪表盘页面数据
- `window.updateSubAccountData()` - 刷新子账号管理页面数据
- `window.updateStoreData()` - 刷新店铺管理页面数据
- `window.updateWorkOrderData()` - 刷新工单面板页面数据
- `window.updateAfterSalesData()` - 刷新售后工单页面数据

### 批量更新
如果需要同时更新多个页面，可以依次调用对应的更新函数：

```javascript
// 修改数据后刷新对应页面
window.table_zizhanghao = newData
window.updateSubAccountData()

// 或者在Debug面板中使用"刷新所有页面数据"按钮
```

## 注意事项

1. **数据格式**：请确保数据格式与示例保持一致，字段名称和类型必须匹配
2. **数组操作**：直接赋值整个数组比逐个修改元素更可靠
3. **分页同步**：修改数据后记得同步更新 `total` 字段
4. **状态值**：状态字段的值必须使用指定的字符串（如：'正常'/'禁用'，'在线'/'离线'）
5. **时间格式**：时间字段请使用 'YYYY/M/D H:mm:ss' 格式

## 事件回调

当用户在界面上进行操作时，系统会调用 `window.g_click` 函数，传递操作信息：

```javascript
// 示例：用户点击搜索按钮时的回调
window.g_click({
  request: JSON.stringify({
    action: "子账号管理搜索按钮",
    text: JSON.stringify({
      account: "搜索的账号",
      status: "正常",
      online: "在线"
    })
  })
})
```

通过这种方式，客户端可以监听用户操作并做出相应处理。
