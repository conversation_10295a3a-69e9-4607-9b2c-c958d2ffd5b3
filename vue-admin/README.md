# 智客AI管理后台 - Vue 3 版本

基于 Vue 3 + Tailwind CSS 4 重构的现代化管理后台系统，从原有的 LayUI 版本完全迁移而来。

## 🚀 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **CSS框架**: Tailwind CSS 4
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **图表库**: ECharts
- **图标**: iconfont

## 🎨 功能特性

### 核心功能
- ✅ 响应式布局设计
- ✅ 明暗主题切换
- ✅ 侧边栏折叠/展开
- ✅ 路由导航
- ✅ 数据表格展示
- ✅ 图表数据可视化
- ✅ 搜索筛选功能

### 页面功能

#### 1. 仪表盘 (Dashboard)
- 统计卡片展示（子账号、店铺、通知、问题数量）
- 问题分析饼图
- 用量TOP3排行榜
- 近8小时用量趋势图
- 最近消息表格

#### 2. 子账号管理
- 账号搜索筛选
- 账号状态管理
- 分页数据展示

#### 3. 店铺管理
- 店铺名称搜索
- 在线状态筛选
- 店铺数据统计

#### 4. 工单面板
- 多条件搜索筛选
- 事件类型分类
- 平台标识显示

#### 5. 售后工单
- 售后类型筛选
- 商品信息展示
- 金额统计显示

## 🛠️ 开发指南

### 环境要求
- Node.js 16+
- npm 或 yarn

### 项目设置
```sh
npm install
```

### 开发运行
```sh
npm run dev
```

### 构建生产
```sh
npm run build
```

## 📱 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📄 更新日志

### v1.0.0 (2025-01-22)
- ✅ 完成基础项目架构搭建
- ✅ 实现所有核心页面功能
- ✅ 完成响应式布局设计
- ✅ 实现明暗主题切换
- ✅ 完成数据可视化图表
- ✅ 实现基础交互功能

---

**智客AI管理后台** - 现代化、响应式、高性能的管理系统解决方案
