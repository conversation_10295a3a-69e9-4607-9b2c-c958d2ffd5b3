import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref({
    name: '张三',
    position: '管理员',
    avatar: '张',
    balance: '0.00'
  })

  // 登录状态
  const isLoggedIn = ref(true)

  // 权限列表
  const permissions = ref([
    'dashboard:view',
    'sub-account:view',
    'sub-account:manage',
    'store:view',
    'store:manage',
    'work-order:view',
    'after-sales:view'
  ])

  // 更新用户信息
  const updateUserInfo = (info) => {
    userInfo.value = { ...userInfo.value, ...info }
  }

  // 更新余额
  const updateBalance = (balance) => {
    userInfo.value.balance = balance
  }

  // 登录
  const login = (userData) => {
    userInfo.value = userData
    isLoggedIn.value = true
  }

  // 登出
  const logout = () => {
    userInfo.value = {
      name: '',
      position: '',
      avatar: '',
      balance: '0.00'
    }
    isLoggedIn.value = false
  }

  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  return {
    userInfo,
    isLoggedIn,
    permissions,
    updateUserInfo,
    updateBalance,
    login,
    logout,
    hasPermission
  }
})
