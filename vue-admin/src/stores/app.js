import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏折叠状态
  const sidebarCollapsed = ref(false)

  // 全局加载状态
  const loading = ref(false)

  // 消息数量
  const messageCount = ref({
    inbox: 5,
    notification: 5
  })

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
  }

  // 设置加载状态
  const setLoading = (state) => {
    loading.value = state
  }

  // 更新消息数量
  const updateMessageCount = (type, count) => {
    messageCount.value[type] = count
  }

  // 应用配置
  const appConfig = ref({
    title: '智客AI',
    logo: '/src/assets/img/logo.jpg',
    version: '1.0.0'
  })

  return {
    sidebarCollapsed,
    loading,
    messageCount,
    appConfig,
    toggleSidebar,
    setSidebarCollapsed,
    setLoading,
    updateMessageCount
  }
})
