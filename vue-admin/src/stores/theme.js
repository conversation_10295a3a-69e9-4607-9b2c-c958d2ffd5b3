import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 当前主题 'light' | 'dark'
  const currentTheme = ref('light')

  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      currentTheme.value = savedTheme
    } else {
      // 检测系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      currentTheme.value = prefersDark ? 'dark' : 'light'
    }
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    const html = document.documentElement
    const body = document.body

    if (currentTheme.value === 'dark') {
      // 添加自定义暗色主题类
      body.classList.add('dark-theme')
      // 添加 Element Plus 暗色主题类
      html.classList.add('dark')
    } else {
      // 移除自定义暗色主题类
      body.classList.remove('dark-theme')
      // 移除 Element Plus 暗色主题类
      html.classList.remove('dark')
    }
  }

  // 切换主题
  const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
    applyTheme()
    localStorage.setItem('theme', currentTheme.value)
  }

  // 设置主题
  const setTheme = (theme) => {
    currentTheme.value = theme
    applyTheme()
    localStorage.setItem('theme', theme)
  }

  // 监听主题变化
  watch(currentTheme, () => {
    applyTheme()
  })

  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
      currentTheme.value = e.matches ? 'dark' : 'light'
    }
  })

  return {
    currentTheme,
    initTheme,
    toggleTheme,
    setTheme
  }
})
