<template>
  <div class="page-container">
    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="layui-card main-card">
        <div class="layui-card-header">售后工单</div>
        <div class="layui-card-body">
          <div class="list_search">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="searchForm.productName"
                  placeholder="商品名称"
                  clearable
                />
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.afterSalesType"
                  placeholder="售后类型"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="商品不满意" value="unsatisfied" />
                  <el-option label="质量问题" value="quality" />
                  <el-option label="商品损坏" value="damage" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchForm.status"
                  placeholder="售后状态"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="待处理" value="pending" />
                  <el-option label="处理中" value="processing" />
                  <el-option label="已完成" value="completed" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleSearch" class="btn2">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="btn6">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格和分页容器 -->
    <div class="table-section">
      <div class="layui-card main-card table-card">
        <div class="layui-card-body">
          <!-- 表格容器 -->
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              style="width: 100%"
              stripe
              border
              :max-height="tableMaxHeight"
              class="stable-table"
            >
              <el-table-column width="60">
                <template #default="scope">
                  <img :src="scope.row.platformIcon" alt="平台" class="w-6 h-6" />
                </template>
              </el-table-column>
              <el-table-column prop="orderNumber" label="售后编号" min-width="150" />
              <el-table-column label="商品信息" min-width="300">
                <template #default="scope">
                  <p class="text-sm font-medium line-clamp-2" :title="scope.row.productInfo.name">
                    {{ scope.row.productInfo.name }}
                  </p>
                  <div class="text-xs text-gray-500 mt-1">
                    <span>ID：{{ scope.row.productInfo.id }}</span>
                    <span class="ml-2">规格：{{ scope.row.productInfo.spec }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="金额" min-width="180">
                <template #default="scope">
                  <div class="text-sm">
                    <div>付款金额：<span class="font-semibold">¥{{ scope.row.amount.paid }}</span></div>
                    <div>售后金额：<span class="font-semibold text-red-600">¥{{ scope.row.amount.afterSales }}</span></div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="applyTime" label="售后申请时间" min-width="180" />
              <el-table-column label="售后类型" min-width="120">
                <template #default="scope">
                  <el-tag
                    :color="scope.row.typeColor"
                    size="small"
                    style="color: white; border: none;"
                  >
                    {{ scope.row.type }}
                  </el-tag>
                  <p class="text-xs text-gray-500 mt-1">{{ scope.row.status }}</p>
                </template>
              </el-table-column>
              <el-table-column prop="reason" label="申请原因" min-width="200" show-overflow-tooltip />
              <el-table-column label="快递信息" min-width="200">
                <template #default="scope">
                  <p class="text-sm font-medium">{{ scope.row.logistics.company }} {{ scope.row.logistics.number }}</p>
                  <p class="text-sm text-gray-500">{{ scope.row.logistics.status }}</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="scope">
                  <div class="action-buttons">
                    <el-button
                      size="small"
                      type="primary"
                      @click="handleIgnore(scope.$index, scope.row)"
                      class="btn2 action-btn"
                    >
                      忽略
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      @click="handleViewDetail(scope.$index, scope.row)"
                      class="btn4 action-btn"
                    >
                      查看详情
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页容器 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="prev, pager, next"
              prev-text="上一页"
              next-text="下一页"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              class="layui-pagination"
          />
        </div>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = ref({
  productName: '',
  afterSalesType: '',
  status: ''
})

// 表格数据
const tableData = ref([
  {
    platformIcon: '/img/pdd.png',
    orderNumber: '694292551694',
    productInfo: {
      name: '科沃斯新品T80扫地机器人国家补贴扫拖一体人国家补贴',
      id: '*********',
      spec: 't80 x1'
    },
    amount: {
      paid: '2999.00',
      afterSales: '2999.00'
    },
    applyTime: '2025-07-12 13:00:00',
    type: '商品不满意',
    typeColor: '#FF6868',
    status: '待处理',
    reason: '原因内容xxxxxxxxxxxxxxxxxxxx',
    logistics: {
      company: '顺丰',
      number: 'SF694294292551',
      status: '已签收 2025-06-01'
    }
  },
  {
    platformIcon: '/img/pdd.png',
    orderNumber: '694292551695',
    productInfo: {
      name: '小米扫地机器人S10+',
      id: '*********',
      spec: 'S10+ x1'
    },
    amount: {
      paid: '1999.00',
      afterSales: '1999.00'
    },
    applyTime: '2025-07-12 14:00:00',
    type: '质量问题',
    typeColor: '#FFA968',
    status: '处理中',
    reason: '机器人无法正常工作',
    logistics: {
      company: '圆通',
      number: 'YT694294292552',
      status: '运输中'
    }
  },
  {
    platformIcon: '/img/pdd.png',
    orderNumber: '694292551696',
    productInfo: {
      name: 'iPhone 15 Pro Max 256GB 深空黑色',
      id: '*********',
      spec: '256GB 深空黑色'
    },
    amount: {
      paid: '9999.00',
      afterSales: '9999.00'
    },
    applyTime: '2025-07-12 15:00:00',
    type: '商品损坏',
    typeColor: '#68B7FF',
    status: '已完成',
    reason: '收到商品时屏幕已破损',
    logistics: {
      company: '申通',
      number: 'ST694294292553',
      status: '已退回'
    }
  }
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(10)

// 计算表格最大高度
const tableMaxHeight = computed(() => {
  // 基础高度：视口高度减去头部、搜索区域、分页区域等固定高度
  const baseHeight = window.innerHeight - 300 // 预留300px给其他元素
  return Math.max(400, Math.min(600, baseHeight)) // 最小400px，最大600px
})

// 搜索
const handleSearch = () => {
  console.log('搜索:', searchForm.value)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "售后工单搜索按钮",
        text: JSON.stringify(searchForm.value)
      })
    })
  }

  ElMessage.success('搜索功能待实现')
}

// 重置
const handleReset = () => {
  searchForm.value = {
    productName: '',
    afterSalesType: '',
    status: ''
  }

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "售后工单重置按钮"
      })
    })
  }

  ElMessage.info('已重置搜索条件')
}

// 忽略
const handleIgnore = (index, row) => {
  console.log('忽略售后工单:', index, row)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "售后工单忽略",
        key: row.orderNumber,
        data: row
      })
    })
  }

  ElMessage.success('已忽略该售后工单')
}

// 查看详情
const handleViewDetail = (index, row) => {
  console.log('查看售后工单详情:', index, row)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "售后工单查看详情",
        key: row.orderNumber,
        data: row
      })
    })
  }

  ElMessage.info('查看详情功能待实现')
}

// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  console.log(`每页 ${val} 条`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "售后工单分页大小改变",
        pageSize: val
      })
    })
  }
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  console.log(`当前页: ${val}`)

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "售后工单分页按钮",
        page: val
      })
    })
  }
}

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置表格数据到全局变量
  window.table_shouhou = tableData.value
  window.table_shouhou_currentPage = currentPage.value
  window.table_shouhou_pageSize = pageSize.value
  window.table_shouhou_total = total.value
}

// 监听数据变化并更新全局变量
watch([tableData, currentPage, pageSize, total], () => {
  setupGlobalVariables()
}, { deep: true })

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.table_shouhou && Array.isArray(window.table_shouhou)) {
    tableData.value = window.table_shouhou
  }
  if (window.table_shouhou_currentPage) {
    currentPage.value = window.table_shouhou_currentPage
  }
  if (window.table_shouhou_pageSize) {
    pageSize.value = window.table_shouhou_pageSize
  }
     if (window.table_shouhou_total) {
    total.value = window.table_shouhou_total
  }

}

// 暴露更新函数到全局
window.updateAfterSalesData = updateFromGlobalData

onMounted(() => {
  // 初始化数据
  setupGlobalVariables()
})
</script>

<style scoped>
/* 页面容器布局 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: calc(100vh - 120px); /* 减去头部导航高度 */
  padding: 20px;
  box-sizing: border-box;
  margin-top: -32px;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 15px;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子元素收缩 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 16px;
}

/* 表格容器 */
.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.stable-table {
  height: 100%;
}

/* 分页容器 */
.pagination-wrapper {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.stable-table .el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100% - 60px); /* 减去表头高度 */
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-button.btn2) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

:deep(.el-button.btn2:hover) {
  background-color: #6639e6;
  border-color: #6639e6;
}

:deep(.el-button.btn4) {
  background-color: #E67162;
  border-color: #E67162;
  color: #fff;
}

:deep(.el-button.btn4:hover) {
  background-color: #d85a4a;
  border-color: #d85a4a;
}

:deep(.el-button.btn6) {
  border: 1px solid #9494AA;
  color: #9494AA;
  
}

:deep(.el-button.btn6:hover) {
  background-color: #f5f5f5;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
}

.action-btn {
  width: 80px !important;
  height: 28px !important;
  font-size: 12px !important;
  margin: 0 !important;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Layui 风格分页样式 */
:deep(.layui-pagination) {
  justify-content: center;
}

:deep(.layui-pagination .btn-next),
:deep(.layui-pagination .btn-prev) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  padding: 0 15px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .btn-next:hover),
:deep(.layui-pagination .btn-prev:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .btn-next.is-disabled),
:deep(.layui-pagination .btn-prev.is-disabled) {
  color: #c0c4cc;
  background-color: #fff;
  border-color: #e2e2e2;
  cursor: not-allowed;
}

:deep(.layui-pagination .el-pager li) {
  background-color: #fff;
  border: 1px solid #e2e2e2;
  color: #333;
  border-radius: 2px;
  margin: 0 2px;
  min-width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 12px;
}

:deep(.layui-pagination .el-pager li:hover) {
  color: #7748F8;
  border-color: #7748F8;
}

:deep(.layui-pagination .el-pager li.is-active) {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

/* 暗色主题文字颜色适配 */
:deep(.dark .el-table .el-table__cell) {
  color: #ffffff !important;
}

:deep(.dark .el-table th.el-table__cell) {
  color: #ffffff !important;
}

:deep(.dark .el-table td.el-table__cell) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-500) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-900) {
  color: #ffffff !important;
}

:deep(.dark .el-table .font-medium) {
  color: #ffffff !important;
}

:deep(.dark .el-table .font-semibold) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-red-600) {
  color: #ff6b6b !important;
}

:deep(.dark .el-table .text-sm) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-xs) {
  color: #ffffff !important;
}

/* 强制所有表格内容为白色 */
:deep(.dark .el-table p) {
  color: #ffffff !important;
}

:deep(.dark .el-table span) {
  color: #ffffff !important;
}

:deep(.dark .el-table div) {
  color: #ffffff !important;
}

:deep(.dark .el-table .el-table__cell > *) {
  color: #ffffff !important;
}

/* 覆盖 Tailwind 颜色类 */
:deep(.dark .el-table .text-gray-900) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-500) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-600) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-700) {
  color: #ffffff !important;
}

:deep(.dark .el-table .text-gray-800) {
  color: #ffffff !important;
}

/* 针对具体的商品名称样式 */
:deep(.dark .el-table p.text-sm.text-gray-900) {
  color: #ffffff !important;
}

:deep(.dark .el-table p.line-clamp-2) {
  color: #ffffff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
    min-height: calc(100vh - 80px);
  }

  .pagination-wrapper {
    padding: 12px 0;
  }

  :deep(.layui-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .table-wrapper {
    overflow-x: auto;
  }

  :deep(.stable-table) {
    min-width: 1200px;
  }
}
</style>
