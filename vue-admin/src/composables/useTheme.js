import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'

export function useTheme() {
  const themeStore = useThemeStore()

  // 当前主题
  const currentTheme = computed(() => themeStore.currentTheme)

  // 是否为暗色主题
  const isDark = computed(() => themeStore.currentTheme === 'dark')

  // 主题图标
  const themeIcon = computed(() => {
    return isDark.value ? '&#xe611;' : '&#xe62e;'
  })

  // 主题文本
  const themeText = computed(() => {
    return isDark.value ? '亮色' : '暗色'
  })

  // 切换主题
  const toggleTheme = () => {
    themeStore.toggleTheme()
  }

  // 设置主题
  const setTheme = (theme) => {
    themeStore.setTheme(theme)
  }

  // 初始化主题
  const initTheme = () => {
    themeStore.initTheme()
  }

  return {
    currentTheme,
    isDark,
    themeIcon,
    themeText,
    toggleTheme,
    setTheme,
    initTheme
  }
}
