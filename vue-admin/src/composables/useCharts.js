import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useTheme } from './useTheme'

export function useCharts(containerId) {
  const chartInstance = ref(null)
  const { isDark } = useTheme()
  
  // 初始化图表
  const initChart = async (options) => {
    await nextTick()
    const container = document.getElementById(containerId)
    if (!container) {
      console.error(`找不到图表容器: ${containerId}`)
      return
    }
    
    // 销毁已存在的实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }
    
    // 创建新实例
    chartInstance.value = echarts.init(container, isDark.value ? 'dark' : 'light')
    chartInstance.value.setOption(options)
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }
  
  // 更新图表配置
  const updateChart = (options, notMerge = false) => {
    if (chartInstance.value) {
      chartInstance.value.setOption(options, notMerge)
    }
  }
  
  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize()
    }
  }
  
  // 显示加载动画
  const showLoading = (text = '加载中...') => {
    if (chartInstance.value) {
      chartInstance.value.showLoading('default', {
        text,
        color: '#7748f8',
        textColor: isDark.value ? '#ffffff' : '#333333',
        maskColor: isDark.value ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)'
      })
    }
  }
  
  // 隐藏加载动画
  const hideLoading = () => {
    if (chartInstance.value) {
      chartInstance.value.hideLoading()
    }
  }
  
  // 获取图表实例
  const getChartInstance = () => {
    return chartInstance.value
  }
  
  // 销毁图表
  const destroyChart = () => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    window.removeEventListener('resize', handleResize)
  }
  
  // 组件卸载时销毁图表
  onUnmounted(() => {
    destroyChart()
  })
  
  return {
    chartInstance,
    initChart,
    updateChart,
    showLoading,
    hideLoading,
    getChartInstance,
    destroyChart,
    handleResize
  }
}

// 预设的图表配置
export const chartConfigs = {
  // 饼图配置
  pie: (data, title = '') => ({
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: '50%',
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }),
  
  // 柱状图配置
  bar: (xData, yData, title = '') => ({
    title: {
      text: title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: yData,
        type: 'bar',
        itemStyle: {
          color: '#7748f8'
        }
      }
    ]
  }),
  
  // 折线图配置
  line: (xData, yData, title = '') => ({
    title: {
      text: title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: yData,
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#7748f8'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(119, 72, 248, 0.3)' },
              { offset: 1, color: 'rgba(119, 72, 248, 0.1)' }
            ]
          }
        }
      }
    ]
  })
}
