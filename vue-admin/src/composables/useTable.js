import { ref, computed, reactive } from 'vue'

export function useTable(options = {}) {
  // 表格数据
  const tableData = ref([])
  
  // 加载状态
  const loading = ref(false)
  
  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: options.pageSize || 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100']
  })
  
  // 搜索表单
  const searchForm = ref({})
  
  // 排序配置
  const sorter = ref({
    field: '',
    order: '' // 'asc' | 'desc'
  })
  
  // 计算分页后的数据
  const paginatedData = computed(() => {
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    return tableData.value.slice(start, end)
  })
  
  // 设置表格数据
  const setTableData = (data) => {
    tableData.value = data
    pagination.total = data.length
  }
  
  // 加载数据
  const loadData = async (params = {}) => {
    loading.value = true
    try {
      // 这里可以调用API获取数据
      // const response = await api.getData(params)
      // setTableData(response.data)
      
      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 500))
      
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 搜索
  const handleSearch = (values) => {
    searchForm.value = values
    pagination.current = 1
    loadData({ ...values, ...pagination })
  }
  
  // 重置搜索
  const handleReset = () => {
    searchForm.value = {}
    pagination.current = 1
    loadData({ ...pagination })
  }
  
  // 分页变化
  const handlePageChange = (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadData({ ...searchForm.value, ...pagination })
  }
  
  // 排序变化
  const handleSortChange = (field, order) => {
    sorter.value = { field, order }
    loadData({ ...searchForm.value, ...pagination, ...sorter.value })
  }
  
  // 刷新数据
  const refresh = () => {
    loadData({ ...searchForm.value, ...pagination, ...sorter.value })
  }
  
  return {
    tableData,
    paginatedData,
    loading,
    pagination,
    searchForm,
    sorter,
    setTableData,
    loadData,
    handleSearch,
    handleReset,
    handlePageChange,
    handleSortChange,
    refresh
  }
}
