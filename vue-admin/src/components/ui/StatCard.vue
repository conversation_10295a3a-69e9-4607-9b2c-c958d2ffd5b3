<template>
  <div 
    class="stat-card bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-md"
    :class="cardClass"
  >
    <div class="flex items-center">
      <!-- 图标区域 -->
      <div 
        class="flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center"
        :class="iconBgClass"
      >
        <IconFont 
          :icon="icon" 
          :color="iconColor"
          size="lg"
        />
      </div>
      
      <!-- 内容区域 -->
      <div class="ml-4 flex-1">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
          {{ title }}
        </p>
        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
          {{ value }}
        </p>
      </div>
    </div>
    
    <!-- 趋势指示器 (可选) -->
    <div v-if="trend" class="mt-3 flex items-center text-sm">
      <IconFont 
        :icon="trendIcon" 
        :color="trendColor"
        size="sm"
      />
      <span :class="trendTextClass" class="ml-1">
        {{ trend.text }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import IconFont from '@/components/icons/IconFont.vue'

const props = defineProps({
  // 标题
  title: {
    type: String,
    required: true
  },
  // 数值
  value: {
    type: [String, Number],
    required: true
  },
  // 图标
  icon: {
    type: String,
    required: true
  },
  // 颜色主题
  color: {
    type: String,
    default: 'blue',
    validator: (value) => ['blue', 'green', 'purple', 'orange', 'red'].includes(value)
  },
  // 趋势数据 (可选)
  trend: {
    type: Object,
    default: null
    // 格式: { type: 'up' | 'down', text: '12%', value: 12 }
  }
})

// 颜色配置
const colorConfig = {
  blue: {
    icon: '#3468e5',
    iconBg: 'bg-blue-100 dark:bg-blue-900',
    card: 'border-l-4 border-blue-500'
  },
  green: {
    icon: '#11cea2',
    iconBg: 'bg-green-100 dark:bg-green-900',
    card: 'border-l-4 border-green-500'
  },
  purple: {
    icon: '#7748f8',
    iconBg: 'bg-purple-100 dark:bg-purple-900',
    card: 'border-l-4 border-purple-500'
  },
  orange: {
    icon: '#f5840a',
    iconBg: 'bg-orange-100 dark:bg-orange-900',
    card: 'border-l-4 border-orange-500'
  },
  red: {
    icon: '#e76537',
    iconBg: 'bg-red-100 dark:bg-red-900',
    card: 'border-l-4 border-red-500'
  }
}

// 计算样式类
const iconColor = computed(() => colorConfig[props.color].icon)
const iconBgClass = computed(() => colorConfig[props.color].iconBg)
const cardClass = computed(() => colorConfig[props.color].card)

// 趋势相关计算
const trendIcon = computed(() => {
  if (!props.trend) return ''
  return props.trend.type === 'up' ? '&#xe625;' : '&#xe626;'
})

const trendColor = computed(() => {
  if (!props.trend) return ''
  return props.trend.type === 'up' ? '#11cea2' : '#e76537'
})

const trendTextClass = computed(() => {
  if (!props.trend) return ''
  return props.trend.type === 'up' 
    ? 'text-green-600 dark:text-green-400' 
    : 'text-red-600 dark:text-red-400'
})
</script>

<style scoped>
.stat-card {
  @apply transform transition-transform duration-200;
}

.stat-card:hover {
  @apply scale-105;
}
</style>
