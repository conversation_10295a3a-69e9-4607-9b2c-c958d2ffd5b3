<template>
  <div class="data-table">
    <!-- 表格 -->
    <el-table
      :data="tableData"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      :height="height"
      :max-height="maxHeight"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      class="custom-table"
    >
      <!-- 多选列 -->
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
        align="center"
        fixed="left"
      />
      
      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        label="序号"
        width="60"
        align="center"
        fixed="left"
      />
      
      <!-- 数据列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :fixed="column.fixed"
        :sortable="column.sortable"
        :align="column.align || 'left'"
        :show-overflow-tooltip="column.showOverflowTooltip !== false"
      >
        <template #default="{ row, column: col, $index }">
          <!-- 自定义插槽 -->
          <slot
            v-if="column.slot"
            :name="column.slot"
            :row="row"
            :column="col"
            :index="$index"
          />
          <!-- 状态标签 -->
          <el-tag
            v-else-if="column.type === 'tag'"
            :type="getTagType(row[column.prop], column.tagMap)"
            size="small"
          >
            {{ getTagText(row[column.prop], column.tagMap) }}
          </el-tag>
          <!-- 金额格式化 -->
          <span v-else-if="column.type === 'money'">
            ¥{{ formatMoney(row[column.prop]) }}
          </span>
          <!-- 日期格式化 -->
          <span v-else-if="column.type === 'date'">
            {{ formatDate(row[column.prop]) }}
          </span>
          <!-- 图片 -->
          <el-image
            v-else-if="column.type === 'image'"
            :src="row[column.prop]"
            :preview-src-list="[row[column.prop]]"
            style="width: 40px; height: 40px"
            fit="cover"
          />
          <!-- 默认文本 -->
          <span v-else>{{ row[column.prop] }}</span>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column
        v-if="showActions"
        label="操作"
        :width="actionWidth"
        fixed="right"
        align="center"
      >
        <template #default="{ row, $index }">
          <slot name="actions" :row="row" :index="$index">
            <el-button
              v-for="action in actions"
              :key="action.key"
              :type="action.type || 'primary'"
              :size="action.size || 'small'"
              :icon="action.icon"
              :disabled="action.disabled && action.disabled(row)"
              @click="handleAction(action.key, row, $index)"
            >
              {{ action.label }}
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div v-if="showPagination" class="pagination-wrapper mt-4 flex justify-end">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 列配置
  columns: {
    type: Array,
    required: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: undefined
  },
  // 最大高度
  maxHeight: {
    type: [String, Number],
    default: undefined
  },
  // 是否显示多选
  showSelection: {
    type: Boolean,
    default: false
  },
  // 是否显示序号
  showIndex: {
    type: Boolean,
    default: false
  },
  // 是否显示操作列
  showActions: {
    type: Boolean,
    default: true
  },
  // 操作列宽度
  actionWidth: {
    type: [String, Number],
    default: 150
  },
  // 操作按钮配置
  actions: {
    type: Array,
    default: () => []
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: true
  },
  // 当前页
  currentPage: {
    type: Number,
    default: 1
  },
  // 每页条数
  pageSize: {
    type: Number,
    default: 10
  },
  // 总条数
  total: {
    type: Number,
    default: 0
  },
  // 每页条数选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  }
})

const emit = defineEmits([
  'sort-change',
  'selection-change',
  'action-click',
  'size-change',
  'current-change'
])

// 表格数据
const tableData = computed(() => props.data)

// 获取标签类型
const getTagType = (value, tagMap) => {
  if (!tagMap) return 'primary'
  const config = tagMap[value]
  return config?.type || 'primary'
}

// 获取标签文本
const getTagText = (value, tagMap) => {
  if (!tagMap) return value
  const config = tagMap[value]
  return config?.text || value
}

// 格式化金额
const formatMoney = (value) => {
  if (!value && value !== 0) return '0.00'
  return Number(value).toFixed(2)
}

// 格式化日期
const formatDate = (value) => {
  if (!value) return ''
  const date = new Date(value)
  return date.toLocaleString('zh-CN')
}

// 排序变化
const handleSortChange = (sortInfo) => {
  emit('sort-change', sortInfo)
}

// 选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 操作按钮点击
const handleAction = (key, row, index) => {
  emit('action-click', { key, row, index })
}

// 每页条数变化
const handleSizeChange = (size) => {
  emit('size-change', size)
}

// 当前页变化
const handleCurrentChange = (page) => {
  emit('current-change', page)
}
</script>

<style scoped>
.data-table {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.dark .data-table {
  background-color: #374151;
  border-color: #4b5563;
}

.pagination-wrapper {
  padding: 0.75rem 1rem;
  border-top: 1px solid #e5e7eb;
}

.dark .pagination-wrapper {
  border-color: #4b5563;
}

:deep(.custom-table) {
  border-radius: 0.5rem;
  overflow: hidden;
}

:deep(.custom-table .el-table__header-wrapper) {
  background-color: #f9fafb;
}

.dark :deep(.custom-table .el-table__header-wrapper) {
  background-color: #4b5563;
}

:deep(.custom-table .el-table__header th) {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 500;
}

.dark :deep(.custom-table .el-table__header th) {
  background-color: #4b5563;
  color: #d1d5db;
}

:deep(.custom-table .el-table__row) {
  transition: background-color 0.2s;
}

:deep(.custom-table .el-table__row:hover) {
  background-color: #f9fafb;
}

.dark :deep(.custom-table .el-table__row:hover) {
  background-color: #4b5563;
}

:deep(.custom-table .el-table__row td) {
  color: #111827;
}

.dark :deep(.custom-table .el-table__row td) {
  color: #f3f4f6;
}

:deep(.el-pagination) {
  color: #374151;
}

.dark :deep(.el-pagination) {
  color: #d1d5db;
}
</style>
