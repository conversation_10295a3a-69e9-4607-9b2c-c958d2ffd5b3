<template>
  <Teleport to="body">
    <div
      v-if="visible"
      ref="menuRef"
      class="context-menu"
      :style="menuStyle"
      @click.stop
    >
      <el-menu
        class="context-menu-list"
        @select="handleMenuSelect"
      >
        <template v-for="(item, index) in menuItems" :key="item.action">
          <el-menu-item
            :index="item.action"
            :class="item.type"
          >
            <el-icon v-if="item.icon">
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.label }}</span>
          </el-menu-item>
          
          <!-- 在清空操作后添加分隔线 -->
          <div 
            v-if="index === 1" 
            class="context-menu-divider"
          ></div>
        </template>
      </el-menu>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  RefreshRight,
  Edit,
  Delete
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 可以通过props传入自定义菜单项
  customMenuItems: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['menu-click'])

// 响应式数据
const visible = ref(false)
const menuRef = ref(null)
const currentRowData = ref(null)
const menuPosition = reactive({
  x: 0,
  y: 0
})

// 防抖定时器
let showTimer = null

// 默认菜单项配置
const defaultMenuItems = [
  {
    action: '店铺管理_清空进线次数',
    label: '清空进线次数',
    icon: RefreshRight,
    type: 'normal'
  },
  {
    action: '店铺管理_清空转接次数',
    label: '清空转接次数',
    icon: RefreshRight,
    type: 'normal'
  }
]

// 计算菜单项
const menuItems = computed(() => {
  return props.customMenuItems.length > 0 ? props.customMenuItems : defaultMenuItems
})

// 计算菜单样式
const menuStyle = computed(() => ({
  position: 'fixed',
  left: `${menuPosition.x}px`,
  top: `${menuPosition.y}px`,
  zIndex: 9999
}))

// 显示菜单
const show = (x, y, rowData) => {
  // 清除之前的定时器
  if (showTimer) {
    clearTimeout(showTimer)
  }

  // 如果菜单已显示，先隐藏
  if (visible.value) {
    hide()
  }

  // 防抖处理，避免快速连续右键
  showTimer = setTimeout(() => {
    currentRowData.value = rowData
    menuPosition.x = x
    menuPosition.y = y
    visible.value = true
    
    // 下一帧调整位置，确保菜单已渲染
    nextTick(() => {
      adjustPosition()
    })
  }, 10)
}

// 隐藏菜单
const hide = () => {
  // 清除定时器
  if (showTimer) {
    clearTimeout(showTimer)
    showTimer = null
  }
  
  visible.value = false
  currentRowData.value = null
}

// 调整菜单位置，防止超出视口
const adjustPosition = () => {
  if (!menuRef.value) return
  
  const menu = menuRef.value
  const menuRect = menu.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const padding = 10 // 距离边界的最小距离
  
  // 调整水平位置
  if (menuPosition.x + menuRect.width > viewportWidth - padding) {
    // 如果右侧空间不足，尝试显示在鼠标左侧
    const leftPosition = menuPosition.x - menuRect.width
    if (leftPosition >= padding) {
      menuPosition.x = leftPosition
    } else {
      // 如果左侧也不足，则贴右边界
      menuPosition.x = viewportWidth - menuRect.width - padding
    }
  }
  
  // 调整垂直位置
  if (menuPosition.y + menuRect.height > viewportHeight - padding) {
    // 如果下方空间不足，尝试显示在鼠标上方
    const topPosition = menuPosition.y - menuRect.height
    if (topPosition >= padding) {
      menuPosition.y = topPosition
    } else {
      // 如果上方也不足，则贴下边界
      menuPosition.y = viewportHeight - menuRect.height - padding
    }
  }
  
  // 确保不超出左边界和上边界
  if (menuPosition.x < padding) menuPosition.x = padding
  if (menuPosition.y < padding) menuPosition.y = padding
}

// 处理菜单项选择
const handleMenuSelect = async (action) => {
  const rowData = currentRowData.value
  
  // 对于删除操作，显示确认对话框
  if (action === '店铺管理_删除店铺') {
    try {
      await ElMessageBox.confirm(
        `确定要删除店铺 "${rowData?.name}" 吗？此操作不可恢复！`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error',
          dangerouslyUseHTMLString: false
        }
      )
      
      // 用户确认删除，继续执行
      executeMenuAction(action, rowData)
    } catch {
      // 用户取消删除
      ElMessage.info('已取消删除')
    }
  } else if (action === '店铺管理_清空进线次数') {
    try {
      await ElMessageBox.confirm(
        `确定要清空店铺 "${rowData?.name}" 的进线次数吗？`,
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
      
      executeMenuAction(action, rowData)
    } catch {
      ElMessage.info('已取消操作')
    }
  } else if (action === '店铺管理_清空转接次数') {
    try {
      await ElMessageBox.confirm(
        `确定要清空店铺 "${rowData?.name}" 的转接次数吗？`,
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
      
      executeMenuAction(action, rowData)
    } catch {
      ElMessage.info('已取消操作')
    }
  } else {
    // 其他操作直接执行
    executeMenuAction(action, rowData)
  }
  
  // 隐藏菜单
  hide()
}

// 执行菜单操作
const executeMenuAction = (action, rowData) => {
  // 调用客户端函数，保持与现有交互机制一致
  if (window.g_click) {
    const actionMap = {
      '店铺管理_清空进线次数': '清空进线次数',
      '店铺管理_清空转接次数': '清空转接次数',
      '店铺管理_编辑店铺': '编辑店铺',
      '店铺管理_删除店铺': '删除店铺'
    }
    
    window.g_click({
      request: JSON.stringify({
        action: actionMap[action] || action,
        storeName: rowData?.name,
        account: rowData?.account,
        data: rowData
      })
    })
  }

  // 发射事件给父组件
  emit('menu-click', {
    action,
    data: rowData
  })
}

// 全局点击事件处理
const handleGlobalClick = (event) => {
  if (visible.value && menuRef.value && !menuRef.value.contains(event.target)) {
    hide()
  }
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && visible.value) {
    hide()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 清理事件监听器
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('keydown', handleKeydown)
  
  // 清理定时器
  if (showTimer) {
    clearTimeout(showTimer)
    showTimer = null
  }
})

// 暴露方法给父组件
defineExpose({
  show,
  hide
})
</script>

<style scoped>
.context-menu {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  padding: 6px 0;
  min-width: 180px;
  max-width: 240px;
  user-select: none;
  animation: contextMenuFadeIn 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.context-menu-list {
  border: none;
  background: transparent;
  padding: 0;
}

.context-menu-list .el-menu-item {
  height: 38px;
  line-height: 38px;
  padding: 0 16px;
  margin: 2px 6px;
  border-radius: 6px;
  color: var(--el-text-color-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.context-menu-list .el-menu-item:hover {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  transform: translateX(2px);
}

.context-menu-list .el-menu-item:active {
  transform: translateX(1px);
  transition-duration: 0.1s;
}

/* 删除店铺样式 */
.context-menu-list .el-menu-item.danger {
  color: #ff5353;
  font-weight: 500;
}

.context-menu-list .el-menu-item.danger:hover {
  background-color: rgba(255, 83, 83, 0.1);
  color: #ff5353;
  box-shadow: 0 2px 8px rgba(255, 83, 83, 0.15);
}

/* 普通菜单项样式 */
.context-menu-list .el-menu-item.normal:hover {
  box-shadow: 0 2px 6px var(--el-box-shadow-light);
}

.context-menu-list .el-menu-item .el-icon {
  margin-right: 10px;
  font-size: 16px;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.context-menu-list .el-menu-item:hover .el-icon {
  transform: scale(1.1);
}

/* 分隔线样式 */
.context-menu-divider {
  height: 1px;
  background-color: var(--el-border-color-lighter);
  margin: 6px 12px;
}

/* 暗色主题适配 */
.dark .context-menu {
  background: var(--el-bg-color-page);
  border-color: var(--el-border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .context-menu-list .el-menu-item {
  color: var(--el-text-color-primary);
}

.dark .context-menu-list .el-menu-item:hover {
  background-color: var(--el-fill-color-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .context-menu {
    min-width: 160px;
    font-size: 13px;
  }
  
  .context-menu-list .el-menu-item {
    height: 36px;
    line-height: 36px;
    padding: 0 12px;
  }
  
  .context-menu-list .el-menu-item .el-icon {
    font-size: 14px;
    margin-right: 8px;
  }
}
</style>
