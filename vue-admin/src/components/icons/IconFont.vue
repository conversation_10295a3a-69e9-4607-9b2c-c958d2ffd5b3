<template>
  <i 
    class="iconfont"
    :class="[
      `text-${size}`,
      colorClass,
      { 'cursor-pointer': clickable }
    ]"
    :style="customStyle"
    v-html="icon"
    @click="handleClick"
  />
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 图标代码 (如: '&#xe6d2;')
  icon: {
    type: String,
    required: true
  },
  // 图标大小
  size: {
    type: String,
    default: 'base',
    validator: (value) => ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl'].includes(value)
  },
  // 图标颜色
  color: {
    type: String,
    default: ''
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

// 颜色类名
const colorClass = computed(() => {
  if (!props.color) return ''
  
  // 预设颜色
  const presetColors = {
    primary: 'text-purple-600',
    success: 'text-green-500',
    warning: 'text-orange-500',
    danger: 'text-red-500',
    info: 'text-blue-500',
    muted: 'text-gray-400'
  }
  
  return presetColors[props.color] || `text-${props.color}`
})

// 自定义样式
const customStyle = computed(() => {
  const style = {}
  
  // 如果传入的是十六进制颜色值
  if (props.color && props.color.startsWith('#')) {
    style.color = props.color
  }
  
  return style
})

// 点击处理
const handleClick = (event) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped>
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: color 0.3s ease;
}

.iconfont:hover {
  opacity: 0.8;
}
</style>
