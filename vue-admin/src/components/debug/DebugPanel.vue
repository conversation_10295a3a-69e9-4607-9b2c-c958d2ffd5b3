<template>
  <div class="debug-panel" v-show="showDebug">
    <div class="debug-header" @click="toggleDebug">
      <i class="iconfont">&#xe6d2;</i>
      <span>Debug 调试面板</span>
      <i class="iconfont toggle-icon" :class="{ 'collapsed': !expanded }">&#xe622;</i>
    </div>
    
    <div class="debug-content" v-show="expanded">
      <div class="debug-section">
        <h4>数据测试</h4>
        <div class="debug-buttons">
          <button @click="testDashboardData" class="debug-btn">
            仪表盘数据测试
          </button>
          <button @click="testSubAccountData" class="debug-btn">
            子账号管理插入数据测试
          </button>
          <button @click="testStoreData" class="debug-btn">
            店铺管理插入数据测试
          </button>
          <button @click="testWorkOrderData" class="debug-btn">
            工单面板插入数据测试
          </button>
          <button @click="testAfterSalesData" class="debug-btn">
            售后工单插入数据测试
          </button>
        </div>
      </div>
      
      <div class="debug-section">
        <h4>数据操作</h4>
        <div class="debug-buttons">
          <button @click="testUserInfo" class="debug-btn">
            用户信息测试
          </button>
          <button @click="updateAllData" class="debug-btn">
            刷新所有页面数据
          </button>
          <button @click="clearAllData" class="debug-btn danger">
            清空所有数据
          </button>
          <button @click="showGlobalVars" class="debug-btn">
            查看全局变量
          </button>
        </div>
      </div>
      
      <div class="debug-section" v-if="logMessages.length > 0">
        <h4>操作日志</h4>
        <div class="debug-log">
          <div 
            v-for="(msg, index) in logMessages" 
            :key="index"
            class="log-item"
            :class="msg.type"
          >
            <span class="log-time">{{ msg.time }}</span>
            <span class="log-content">{{ msg.content }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 显示/隐藏按钮 -->
  <div v-if="false" class="debug-toggle" @click="showDebug = !showDebug" v-show="!showDebug">
    <i class="iconfont">&#xe6d2;</i>
    Debug
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const showDebug = ref(false)
const expanded = ref(true)
const logMessages = ref([])

// 添加日志
const addLog = (content, type = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  logMessages.value.unshift({
    time,
    content,
    type
  })
  
  // 只保留最近20条日志
  if (logMessages.value.length > 20) {
    logMessages.value = logMessages.value.slice(0, 20)
  }
}

// 切换展开/收起
const toggleDebug = () => {
  expanded.value = !expanded.value
}

// 仪表盘测试数据
const testDashboardData = () => {
  // 统计卡片数据
  window.dashboard_stats = {
    subAccountCount: 888,
    storeCount: 666,
    todayNotifications: 999,
    problemCount: { current: 150, total: 200 }
  }

  // 用量TOP3数据
  window.dashboard_topUsage = [
    { name: '测试用户A', usage: 999, avatar: '/img/logo.jpg' },
    { name: '测试用户B', usage: 888, avatar: '/img/logo.jpg' },
    { name: '测试用户C', usage: 777, avatar: '/img/logo.jpg' }
  ]

  // 最近消息数据
  window.dashboard_recentMessages = [
    {
      store: '测试店铺',
      user: '测试客户',
      content: '这是一条测试消息内容',
      reply: '这是测试回复内容',
      time: '2025/7/22 12:00:00'
    }
  ]

  // 图表数据
  window.dashboard_chartData = {
    problemChart: {
      xData: ['测试1', '测试2', '测试3', '测试4', '测试5', '测试6', '今天'],
      workOrderData: [200, 300, 250, 350, 300, 400, 250],
      afterSalesData: [80, 120, 100, 150, 130, 180, 120]
    },
    usageChart: {
      xData: ['12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00'],
      yData: [200, 300, 250, 150, 120, 180, 220, 280]
    }
  }

  // 更新仪表盘
  if (window.updateDashboardData) {
    window.updateDashboardData()
  }

  addLog('仪表盘：已更新测试数据', 'success')
}

// 子账号管理测试数据
const testSubAccountData = () => {
  const testData = []

  // 生成100条测试数据
  for (let i = 1; i <= 100; i++) {
    const accountNumber = String(i).padStart(6, '0')
    const levels = [0, 1, 2]
    const statuses = ['正常', '禁用']
    const onlineStatuses = ['在线', '离线']

    // 随机选择状态
    const level = levels[Math.floor(Math.random() * levels.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const online = onlineStatuses[Math.floor(Math.random() * onlineStatuses.length)]

    // 根据等级设置余额范围
    let balanceRange
    switch (level) {
      case 0:
        balanceRange = [100, 1000]
        break
      case 1:
        balanceRange = [1000, 3000]
        break
      case 2:
        balanceRange = [3000, 10000]
        break
    }

    const balance = (Math.random() * (balanceRange[1] - balanceRange[0]) + balanceRange[0]).toFixed(2)

    // 生成随机日期
    const expireDate = new Date(2025, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
    const loginDate = new Date(2025, 6, Math.floor(Math.random() * 22) + 1, Math.floor(Math.random() * 24), Math.floor(Math.random() * 60))

    const storeCount = Math.floor(Math.random() * 10) + 1
    const onlineStoreCount = online === '在线' ? Math.floor(Math.random() * storeCount) : 0

    testData.push({
      account: `TEST${accountNumber}`,
      level: level,
      balance: balance,
      expireTime: `${expireDate.getFullYear()}/${expireDate.getMonth() + 1}/${expireDate.getDate()} 23:59:59`,
      status: status,
      loginTime: `${loginDate.getFullYear()}/${loginDate.getMonth() + 1}/${loginDate.getDate()} ${loginDate.getHours().toString().padStart(2, '0')}:${loginDate.getMinutes().toString().padStart(2, '0')}:00`,
      online: online,
      storeCount: storeCount,
      onlineStoreCount: onlineStoreCount
    })
  }

  window.table_zizhanghao = testData
  window.table_zizhanghao_currentPage = 1
  window.table_zizhanghao_pageSize = 10
  window.table_zizhanghao_total = testData.length

  // 调用更新函数刷新页面显示
  if (window.updateSubAccountData) {
    window.updateSubAccountData()
  }

  addLog(`子账号管理：插入 ${testData.length} 条测试数据`, 'success')
}

// 店铺管理测试数据
const testStoreData = () => {
  const testData = [
    {
      name: '测试店铺A',
      todayReception: 25,
      pointsConsumed: 180,
      incomingCount: 35,
      transferCount: 5,
      account: 'TEST001',
      createTime: '2025/7/22 09:00:00',
      online: '在线'
    },
    {
      name: '测试店铺B',
      todayReception: 15,
      pointsConsumed: 120,
      incomingCount: 20,
      transferCount: 2,
      account: 'TEST002',
      createTime: '2025/7/22 10:00:00',
      online: '在线'
    }
  ]
  
  window.table_dianpu = testData
  window.table_dianpu_currentPage = 1
  window.table_dianpu_pageSize = 10
  window.table_dianpu_total = testData.length

  // 调用更新函数刷新页面显示
  if (window.updateStoreData) {
    window.updateStoreData()
  }

  addLog(`店铺管理：插入 ${testData.length} 条测试数据`, 'success')
}

// 工单面板测试数据
const testWorkOrderData = () => {
  const testData = [
    {
      eventType: '测试事件',
      eventColor: '#FF6868',
      platformIcon: '/img/pdd.png',
      storeName: '测试店铺',
      productName: '测试商品名称',
      customerName: '测试客户',
      orderNumber: 'TEST' + Date.now(),
      trackingNumber: 'SF' + Date.now(),
      logisticsStatus: '运输中',
      description: '这是一个测试工单'
    }
  ]
  
  window.table_gongdan = testData
  window.table_gongdan_currentPage = 1
  window.table_gongdan_pageSize = 10
  window.table_gongdan_total = testData.length

  // 调用更新函数刷新页面显示
  if (window.updateWorkOrderData) {
    window.updateWorkOrderData()
  }

  addLog(`工单面板：插入 ${testData.length} 条测试数据`, 'success')
}

// 售后工单测试数据
const testAfterSalesData = () => {
  const testData = [
    {
      platformIcon: '/img/pdd.png',
      orderNumber: 'AS' + Date.now(),
      productInfo: {
        name: '测试售后商品',
        id: 'TEST123',
        spec: '测试规格'
      },
      amount: {
        paid: '1999.00',
        afterSales: '1999.00'
      },
      applyTime: '2025-07-22 12:00:00',
      type: '测试售后',
      typeColor: '#FF6868',
      status: '待处理',
      reason: '测试售后原因',
      logistics: {
        company: '测试快递',
        number: 'TEST' + Date.now(),
        status: '待退回'
      }
    }
  ]
  
  window.table_shouhou = testData
  window.table_shouhou_currentPage = 1
  window.table_shouhou_pageSize = 10
  window.table_shouhou_total = testData.length

  // 调用更新函数刷新页面显示
  if (window.updateAfterSalesData) {
    window.updateAfterSalesData()
  }

  addLog(`售后工单：插入 ${testData.length} 条测试数据`, 'success')
}

// 清空所有数据
const clearAllData = () => {
  // 清空用户信息
  window.sidebar_userInfo = { avatar: '', name: '', position: '', balance: '0.00' }

  // 清空仪表盘数据
  window.dashboard_stats = { subAccountCount: 0, storeCount: 0, todayNotifications: 0, problemCount: { current: 0, total: 0 } }
  window.dashboard_topUsage = []
  window.dashboard_recentMessages = []
  window.dashboard_chartData = {
    problemChart: { xData: [], workOrderData: [], afterSalesData: [] },
    usageChart: { xData: [], yData: [] }
  }

  // 清空表格数据
  window.table_zizhanghao = []
  window.table_zizhanghao_total = 0
  window.table_dianpu = []
  window.table_dianpu_total = 0
  window.table_gongdan = []
  window.table_gongdan_total = 0
  window.table_shouhou = []
  window.table_shouhou_total = 0

  // 更新所有页面
  updateAllData()

  addLog('已清空所有数据', 'warning')
}

// 用户信息测试
const testUserInfo = () => {
  window.sidebar_userInfo = {
    avatar: '测',
    name: '测试用户',
    position: '超级管理员',
    balance: '9999.99'
  }

  // 更新侧边栏用户信息
  if (window.updateSidebarUserInfo) {
    window.updateSidebarUserInfo()
  }

  addLog('用户信息：已更新测试数据', 'success')
}

// 刷新所有页面数据
const updateAllData = () => {
  let updatedCount = 0

  if (window.updateSidebarUserInfo) {
    window.updateSidebarUserInfo()
    updatedCount++
  }

  if (window.updateDashboardData) {
    window.updateDashboardData()
    updatedCount++
  }

  if (window.updateSubAccountData) {
    window.updateSubAccountData()
    updatedCount++
  }

  if (window.updateStoreData) {
    window.updateStoreData()
    updatedCount++
  }

  if (window.updateWorkOrderData) {
    window.updateWorkOrderData()
    updatedCount++
  }

  if (window.updateAfterSalesData) {
    window.updateAfterSalesData()
    updatedCount++
  }

  addLog(`已刷新 ${updatedCount} 个页面的数据`, 'success')
}

// 查看全局变量
const showGlobalVars = () => {
  const vars = {
    用户信息: window.sidebar_userInfo || {},
    仪表盘: {
      统计数据: window.dashboard_stats || {},
      TOP3数据条数: window.dashboard_topUsage?.length || 0,
      消息数据条数: window.dashboard_recentMessages?.length || 0,
      图表数据: window.dashboard_chartData || {}
    },
    子账号管理: {
      数据条数: window.table_zizhanghao?.length || 0,
      当前页: window.table_zizhanghao_currentPage || 0,
      页大小: window.table_zizhanghao_pageSize || 0,
      总数: window.table_zizhanghao_total || 0
    },
    店铺管理: {
      数据条数: window.table_dianpu?.length || 0,
      当前页: window.table_dianpu_currentPage || 0,
      页大小: window.table_dianpu_pageSize || 0,
      总数: window.table_dianpu_total || 0
    },
    工单面板: {
      数据条数: window.table_gongdan?.length || 0,
      当前页: window.table_gongdan_currentPage || 0,
      页大小: window.table_gongdan_pageSize || 0,
      总数: window.table_gongdan_total || 0
    },
    售后工单: {
      数据条数: window.table_shouhou?.length || 0,
      当前页: window.table_shouhou_currentPage || 0,
      页大小: window.table_shouhou_pageSize || 0,
      总数: window.table_shouhou_total || 0
    }
  }

  console.log('全局变量状态:', vars)
  addLog('全局变量状态已输出到控制台', 'info')
}

onMounted(() => {
  addLog('Debug面板已加载', 'info')
})
</script>

<style scoped>
.debug-panel {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 350px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-size: 12px;
}

.debug-header {
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.debug-header span {
  flex: 1;
  margin-left: 8px;
  font-weight: 500;
}

.toggle-icon {
  transition: transform 0.3s ease;
}

.toggle-icon.collapsed {
  transform: rotate(180deg);
}

.debug-content {
  max-height: 400px;
  overflow-y: auto;
}

.debug-section {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.debug-section:last-child {
  border-bottom: none;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #333;
}

.debug-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.debug-btn {
  padding: 6px 12px;
  border: 1px solid #d0d0d0;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.debug-btn:hover {
  background: #f5f5f5;
  border-color: #7748F8;
}

.debug-btn.danger {
  color: #e74c3c;
  border-color: #e74c3c;
}

.debug-btn.danger:hover {
  background: #fdf2f2;
}

.debug-log {
  max-height: 150px;
  overflow-y: auto;
  background: #f9f9f9;
  border-radius: 4px;
  padding: 8px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 11px;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 60px;
}

.log-content {
  flex: 1;
}

.log-item.success .log-content {
  color: #27ae60;
}

.log-item.warning .log-content {
  color: #f39c12;
}

.log-item.error .log-content {
  color: #e74c3c;
}

.debug-toggle {
  display: hidden;
  position: fixed;
  bottom: 20px;
  left: 20px;
  padding: 8px 12px;
  background: #7748F8;
  color: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(119, 72, 248, 0.3);
}

.debug-toggle:hover {
  background: #6639e6;
}

/* 暗色主题适配 */
body.dark-theme .debug-panel {
  background: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}

body.dark-theme .debug-header {
  background: #3a3a3a;
  border-bottom-color: #444;
}

body.dark-theme .debug-section {
  border-bottom-color: #444;
}

body.dark-theme .debug-btn {
  background: #3a3a3a;
  border-color: #555;
  color: #e0e0e0;
}

body.dark-theme .debug-btn:hover {
  background: #404040;
  border-color: #7748F8;
}

body.dark-theme .debug-log {
  background: #333;
}
</style>
