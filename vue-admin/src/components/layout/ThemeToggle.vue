<template>
  <div class="theme-toggle">
    <div 
      class="flex items-center cursor-pointer transition-all duration-300 hover:opacity-80"
      @click="toggleTheme"
    >
      <IconFont 
        :icon="themeIcon" 
        :color="themeIconColor"
        size="base"
        clickable
      />
      <span class="ml-1 text-sm">{{ themeText }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTheme } from '@/composables/useTheme'
import IconFont from '@/components/icons/IconFont.vue'

const { isDark, themeIcon, themeText, toggleTheme } = useTheme()

// 主题图标颜色
const themeIconColor = computed(() => {
  return isDark.value ? '#fffb00' : '#bd48e5'
})
</script>

<style scoped>
.theme-toggle {
  @apply select-none;
}
</style>
