<template>
  <div class="header" style="padding: 10px 0">
    <div class="logo">
      <img src="/img/logo.jpg" alt="Logo">
      <span>智客AI</span>
    </div>
    <div class="flex align-c">
      <div class="flex-0">
        <div class="toggle-btn" :class="{ 'collapsed': sidebarCollapsed }" @click="toggleSidebar" id="toggleBtn">
          <i class="iconfont">&#xe622;</i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()

  // 调用客户端函数
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "侧边栏折叠按钮",
        collapsed: !sidebarCollapsed.value
      })
    })
  }
}
</script>

<style scoped>
/* Header 样式已在 old-styles.css 中定义 */
</style>
