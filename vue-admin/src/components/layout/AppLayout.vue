<template>
  <div class="app-layout min-h-screen transition-colors duration-300" style="background-image: url('/img/bg-main.png'); background-size: cover; background-attachment: fixed; background-position: center; background-repeat: no-repeat;">
    <!-- 顶部导航栏 -->
    <AppHeader />
    
    <!-- 侧边栏 -->
    <AppSidebar />

    <!-- 右上角工具栏 -->
    <RightToolbar />

    <!-- 主内容区域 -->
    <main
      class="main-content"
      :class="{ 'collapsed': sidebarCollapsed }"
    >
      <!-- 路由视图 -->
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    


    <!-- Debug面板 -->
    <DebugPanel />

    <!-- 遮罩层 (移动端) -->
    <div
      v-if="!sidebarCollapsed && isMobile"
      class="sidebar-overlay fixed inset-0 bg-black bg-opacity-50 z-998 md:hidden"
      @click="closeSidebar"
    ></div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useAppStore } from '@/stores/app'
import { useTheme } from '@/composables/useTheme'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'
import RightToolbar from './RightToolbar.vue'
import DebugPanel from '@/components/debug/DebugPanel.vue'

const appStore = useAppStore()
const { initTheme } = useTheme()

// 响应式状态
const isMobile = ref(false)

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value && !sidebarCollapsed.value) {
    appStore.setSidebarCollapsed(true)
  }
}

// 关闭侧边栏 (移动端)
const closeSidebar = () => {
  if (isMobile.value) {
    appStore.setSidebarCollapsed(true)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 生命周期
onMounted(() => {
  // 初始化主题
  initTheme()
  
  // 检查移动端
  checkMobile()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 遮罩层 */
.sidebar-overlay {
  z-index: 998;
}
</style>
