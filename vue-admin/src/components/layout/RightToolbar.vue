<template>
  <div class="right-top">
    <div class="theme-switch" @click="toggleTheme">
      <div class="flex align-c" id="sunIcon" v-show="!isDark">
        <i class="iconfont sun-icon" style="color: #bd48e5">&#xe62e;</i>暗色
      </div>
      <div class="flex align-c" id="moonIcon" v-show="isDark">
        <i class="iconfont moon-icon" style="color: #fffb00">&#xe611;</i>亮色
      </div>
    </div>
    <i class="iconfont" @click="handleMinimize">&#xe62b;</i>
    <i class="iconfont" @click="handleMaximize">&#xe650;</i>
    <i class="iconfont" @click="handleClose">&#xe75c;</i>
  </div>
</template>

<script setup>
import { useTheme } from '@/composables/useTheme'

const { isDark, toggleTheme } = useTheme()

// 最小化按钮
const handleMinimize = () => {
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "最小化按钮"
      })
    })
  }
}

// 最大化按钮
const handleMaximize = () => {
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "最大化按钮"
      })
    })
  }
}

// 关闭按钮
const handleClose = () => {
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "关闭按钮"
      })
    })
  }
}
</script>

<style scoped>
/* 右上角工具栏样式已在 old-styles.css 中定义 */
</style>
