{"name": "vue-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tailwindcss/vite": "^4.1.11", "echarts": "^5.6.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}