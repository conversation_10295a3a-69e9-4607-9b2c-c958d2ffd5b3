// common.js
layui.use(['jquery', 'layer'], function () {
    const $ = layui.$;
    const layer = layui.layer;

    // 侧边栏折叠/展开
    $('#toggleBtn').on('click', function () {
        $('#sidebar').toggleClass('collapsed');
        $('#layout').toggleClass('collapsed');
        $('.toggle-btn').toggleClass('collapsed');
    });

    // 菜单导航功能
    $('.menu-item').on('click', function(){
        var url = $(this).data('url');
        if(url) {
            $('.menu-item').removeClass('active');
            $(this).addClass('active');
            window.location.href = url;
        }
    });

    // 主题切换功能
    // 主题切换功能
    const sunIcon = document.getElementById('sunIcon');
    const moonIcon = document.getElementById('moonIcon');
    if(sunIcon && moonIcon) {
        const body = document.body;

        // 检查本地存储中的主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            body.classList.add('dark-theme');
            sunIcon.style.display = 'none';
            moonIcon.style.display = 'flex';
        } else {
            sunIcon.style.display = 'flex';
            moonIcon.style.display = 'none';
        }

        // 点击太阳图标切换到暗色主题
        sunIcon.addEventListener('click', function() {
            body.classList.add('dark-theme');
            sunIcon.style.display = 'none';
            moonIcon.style.display = 'flex';
            localStorage.setItem('theme', 'dark');

            // 触发主题变化事件
            const event = new Event('themeChanged');
            document.dispatchEvent(event);
        });

        // 点击月亮图标切换到亮色主题
        moonIcon.addEventListener('click', function() {
            body.classList.remove('dark-theme');
            sunIcon.style.display = 'flex';
            moonIcon.style.display = 'none';
            localStorage.setItem('theme', 'light');

            // 触发主题变化事件
            const event = new Event('themeChanged');
            document.dispatchEvent(event);
        });
    }

    // 右键菜单功能
    const contextMenu = $('#contextMenu');
    if(contextMenu.length > 0) {
        let currentRow = null;

        $('.layui-table tbody tr').on('contextmenu', function(e) {
            e.preventDefault();
            currentRow = $(this);

            contextMenu.css({
                display: 'block',
                left: e.pageX + 'px',
                top: e.pageY + 'px'
            });

            $(document).one('click', function() {
                contextMenu.hide();
            });
        });
    }

    // 图表初始化
    function initCharts() {
        const chartElements = {
            'main1': createProblemAnalysisChart,
            'main2': createUsageStatisticsChart
        };

        Object.entries(chartElements).forEach(([id, creator]) => {
            const element = document.getElementById(id);
            if (element) {
                const chart = echarts.init(element);
                creator(chart);

                // 监听主题变化事件
                document.addEventListener('themeChanged', () => {
                    chart.setOption(creator(chart).getOption());
                });

                window.addEventListener('resize', () => chart.resize());
            }
        });
    }

    // 问题分析图表
    function createProblemAnalysisChart(chart) {
        const isDark = document.body.classList.contains('dark-theme');
        const textColor = isDark ? '#fff' : '#333';
        const splitLineColor = isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';

        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' }
            },
            legend: {
                textStyle: { color: textColor }
            },
            grid: {
                left: '30px',
                right: '10px',
                bottom: '30px',
                top: '30px',
            },
            xAxis: {
                type: 'category',
                data: ['07-06', '07-07', '07-08', '07-09', '07-10', '07-11', '今天'],
                axisLabel: { color: textColor }
            },
            yAxis: {
                type: 'value',
                splitLine: { lineStyle: { color: splitLineColor } },
                axisLabel: { color: textColor }
            },
            series: [
                {
                    name: '工单数量',
                    type: 'bar',
                    data: [100, 200, 150, 250, 200, 300, 150],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#6f5dcb' },
                            { offset: 1, color: '#8e7fef' }
                        ])
                    },
                    label: { show: true, position: 'top', color: textColor },
                    barGap: '0%',
                },
                {
                    name: '售后数量',
                    type: 'bar',
                    data: [50, 80, 70, 100, 90, 120, 80],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#a5d5e1' },
                            { offset: 1, color: '#bde8f3' }
                        ])
                    },
                    label: { show: true, position: 'top', color: textColor },
                    barGap: '0%',
                }
            ]
        };

        chart.setOption(option);
        return {
            getOption: () => option
        };
    }

    // 用量统计图表
    function createUsageStatisticsChart(chart) {
        const isDark = document.body.classList.contains('dark-theme');
        const textColor = isDark ? '#fff' : '#333';
        const splitLineColor = isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';

        const option = {
            tooltip: { trigger: 'axis' },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '40px',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: [
                    new Date(Date.now() - 7 * 3600 * 1000).getHours() + ':00',
                    new Date(Date.now() - 6 * 3600 * 1000).getHours() + ':00',
                    new Date(Date.now() - 5 * 3600 * 1000).getHours() + ':00',
                    new Date(Date.now() - 4 * 3600 * 1000).getHours() + ':00',
                    new Date(Date.now() - 3 * 3600 * 1000).getHours() + ':00',
                    new Date(Date.now() - 2 * 3600 * 1000).getHours() + ':00',
                    new Date(Date.now() - 1 * 3600 * 1000).getHours() + ':00',
                    new Date().getHours() + ':00'
                ],
                axisLabel: {
                    show: false, // 隐藏标签
                },
                axisLine: {
                    show: false, // 隐藏轴线（可选）
                },
                axisTick: {
                    show: false, // 隐藏刻度线（可选）
                },
            },
            yAxis: {
                type: 'value',
                name: '数量',
                axisLabel: { color: textColor },
                splitLine: { lineStyle: { color: splitLineColor } }
            },
            series: [{
                name: '用量',
                type: 'line',
                smooth: true,
                data: [120, 200, 150, 80, 70, 110, 130, 180],
                lineStyle: { width: 3, color: '#5588ff' },
                itemStyle: {
                    color: '#5588ff',
                    borderColor: '#fff',
                    borderWidth: 2
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(85, 136, 255, 0.5)' },
                        { offset: 1, color: 'rgba(85, 136, 255, 0.1)' }
                    ])
                },
                symbol: 'circle',
                symbolSize: 8,
                label: { show: true, position: 'top', color: textColor }
            }]
        };

        chart.setOption(option);
        return {
            getOption: () => option
        };
    }

    // 初始化图表
    if (typeof echarts !== 'undefined') {
        initCharts();
    }
});
