body {
  margin: 0;
  padding: 0;
  background-image: url("img/bg-main.png");
  background-size: cover;
}
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 6px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 6px;
}

/* 滚动条滑块悬停状态 */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont';  /* Project id 4973147 */
  src: url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.woff2?t=1752327680529') format('woff2'),
  url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.woff?t=1752327680529') format('woff'),
  url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.ttf?t=1752327680529') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex {
  display: flex;
}

.w-100 {
  width: 100%;
}

.flex-0 {
  flex: 0 0 auto;
}

.align-c {
  align-items: center;
}

.align-t {
  align-items: flex-start;
}

.align-b {
  align-items: flex-end;
}

.justify-l {
  justify-content: flex-start;
}

.justify-c {
  justify-content: center;
}

.justify-r {
  justify-content: flex-end;
}

.justify-y {
  justify-content: space-between;
}

.color1 {
  color: #c2c2c2;
}

.sidebar {
  width: 150px;
  background-color: #ffffff;
  color: #000;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 48px;
  bottom: 20px;
  left: 10px;
  border-radius: 20px;
  padding-top: 10px;

  &.collapsed {
    width: 80px;
    .text3{
      span:nth-child(1){
        display: none;
      }
    }
    .user-info {
      justify-content: center;
      .user-details {
        display: none;
      }
    }
    .logo span {
      display: none;
    }
    .menu-container {
      padding-left: 8px;
      padding-right: 8px;
    }
    .menu-container .menu-item {
      justify-content: center;
      position: relative;
      span {
        display: none;
      }
      i {
        margin-right: 0;
      }
      .message-count {
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }



  .menu-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;


    .line {
      height: 1px;
      background: #e6e6e6;
      margin: 30px 0;
    }

    .menu-item,
    .message-item {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 10px;
      cursor: pointer;
      transition: background-color 0.3s;
      white-space: nowrap;
      border-radius: 10px;
      margin-bottom: 10px;
      font-size: 14px;

      i {
        font-size: 16px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      span {
        opacity: 1;
        transition: opacity 0.3s ease;
      }

      &:hover,
      &:hover {
        background-color: #f5f5f5;
      }

      &.active {
        background-color: #6C5DD3;
        color: #fff;
      }
    }

    .collapsed & .menu-item span,
    .collapsed & .message-item span {
      opacity: 0;
      width: 0;
    }


    .message-count {
      background-color: #FF754C;
      color: white;
      border-radius: 10px;
      padding: 0 6px;
      font-size: 12px;
      margin-left: auto;
      height: 20px;
      line-height: 20px;
      text-align: center;
    }

    .collapsed & .message-count {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .text3 {
    background: #efecff;
    border-radius: 100px;
    padding: 4px 12px;
    text-align: center;
    margin: 0 12px 16px 12px;
    font-size: 12px;
    color: #6C5DD3;
    span:nth-child(1){

    }
    span:nth-child(2){

    }
  }
  .user-info {
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);


    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #6C5DD3;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      color: #fff;
    }

    .user-details {
      margin-left: 12px;
      white-space: nowrap;
      overflow: hidden;

      .user-name {
        font-weight: 500;
      }

      .user-position {
        font-size: 12px;
        opacity: 0.8;
      }
    }


    .collapsed & .user-details {
      opacity: 0;
      width: 0;
    }
  }


}
.toggle-btn {
  position: relative;
  z-index: 92;
  cursor: pointer;
  i {
    color: #000;
    font-size: 18px;
    display: block;
  }
  &.collapsed{
    i{
      transform: rotate(180deg);
    }
  }
}


.main-content {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
  margin-left: 150px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;

  &.collapsed {
    margin-left: 80px;
  }



  .body {
    flex: 1;
  }
}

.header {
  padding: 12px 0;
  display: flex;


  .head {
    width: 30px;
    height: 30px;
    border-radius: 100px;
    margin: 0 8px 0 10px;
  }
  .text1 {
    font-size: 12px;
    margin-left: 12px;
  }
  .text2 {
    font-size: 18px;
    font-weight: bold;

  }


  .logo {
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    position: relative;
    z-index: 92;

    img {
      width: 28px;
      height: 28px;
      border-radius: 8px;
    }

    span {
      margin-left: 12px;
      font-size: 20px;
      font-weight: bold;
      white-space: nowrap;
      opacity: 1;
      transition: opacity 0.3s ease;
    }

    .collapsed & span {
      opacity: 0;
      width: 0;
    }
  }
}


.right-top {
  position: fixed;
  right: 10px;
  top: 0;
  color: #808080;
  display: flex;
  justify-content: flex-end;
  z-index: 90;
  left: 0;
  cursor: all-scroll;
  padding-top: 6px;

  .iconfont {
    font-size: 16px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #efefef;
      cursor: pointer;
    }
  }
}

.main-card {

}

.layui-card.main-card {
  box-shadow: 0 40px 60px 1px rgba(96, 106, 208, 0.1);
  border-radius: 16px;
  background: #fff;
  padding: 8px 12px 12px 12px;

  .layui-card-body {
    padding: 0;
  }

  .layui-card-header {
    border-bottom: none;
    background: none;
    font-size: 15px;
    padding-left: 0;
    font-weight: 600;
    height: 32px;
    line-height: 35px;
  }
}

.ybp_card{
  padding: 16px;
  height: 118px;
  box-sizing: border-box;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  //display: flex;
  //align-items: center;
  &.v1{
    background: #f3f1ff;
  }
  &.v2{
    background: #e4fcf7;
  }
  &.v3{
    background: #eaf2ff;
  }
  &.v4{
    background: #fff5e9;
  }
  .icon{
    width: 38px;
    height: 38px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
    margin-right: 8px;
    //position: absolute;
    //right: 0;
    //bottom: 0;
    //opacity: 0.6;
    i{
      font-size: 20px;
      color: #6C5DD3;
    }
  }
  .text1{
    font-size: 12px;
  }
  .text2{
    font-size: 18px;
    font-weight: bold;
  }
}
.ybp_list{
  .item{
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e6e6e6;
    font-size: 12px;
    &:nth-last-child(1){
      border-bottom: none;

    }
    .text1{
      width: 30px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
    }
    .head{
      width: 30px;
      height: 30px;
      border-radius: 100px;
      border: 2px solid #fff;
      box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
      margin-right: 12px;
    }
  }
}


.qxjc_t4{
  border-spacing: 0 6px;
  border-collapse: separate;
  margin: 0 !important;


  .pt{
    width: 20px;
    height: 20px;
    margin: auto;
  }

  th,td{
    border: none;
    word-wrap: break-word;
    white-space: normal;
    position: relative;
    font-size: 13px;
    &:nth-child(1){
      padding-left: 12px;
    }
  }
  th{
    color: #62687E;
    font-weight: 500;
    padding: 0 5px;
  }
  tr{
    td{
      border-top: 1px solid #E2E2E7;
      border-bottom: 1px solid #E2E2E7;
      color: #1B2441;
      padding: 9px 5px;
      &:nth-child(1){
        border-left: 1px solid #E2E2E7;
        border-radius: 6px 0 0 6px;
      }
      &:nth-last-child(1){
        border-right: 1px solid #E2E2E7;
        border-radius: 0 6px 6px 0;
      }
    }
    &:hover{
      td{
        background: #f5f5f5;
      }
    }
  }
  &.table-fixed{
    table-layout: fixed;
    th,td{
      white-space: nowrap;
    }
  }
}
.text_color1{
  color: #7748F8;
  cursor: pointer;
  &:hover{
    color: #5721ec;
  }
}
.text_color2{
  color: #E67162;
  &:hover{
    color: #c04b3c;
  }
}

.list_search{
  margin: 8px 0 4px 0;
  .layui-input, .layui-select, .layui-textarea{
    background: #F5F5FC;
    border: none;
    border-radius: 8px;
    transition: none;
    &:focus{
      box-shadow: none;
    }
  }
  .layui-input-wrap{
    display: inline-flex;
    width: 300px;
    background: #F5F5FC;
    align-items: center;
    margin-bottom: 0;
    border-radius: 8px;
    vertical-align: top;

    .layui-form-label{
      width: auto;
      padding-right: 0;
      flex: 0 0 auto;
      color: #62687E;
      padding-top: 1px;
      padding-bottom: 0;
    }
    .layui-form-select{
      width: 100%;
    }
  }
  .layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this>a, .layui-nav-tree .layui-this>a:hover{
    background: #7748F8;
  }
  .layui-laydate .layui-this, .layui-laydate .layui-this>div {
    background-color: #7748F8 !important;
  }
  .layui-form-radio:hover>*, .layui-form-radioed, .layui-form-radioed>i,
  .layui-form-select dl dd.layui-this,
  .layui-layout-admin .layui-logo i,
  .layui-laydate-footer span:hover{
    color: #7748F8;
  }
  .layui-form-onswitch,.layui-form-checked[lay-skin=primary]>i{
    border-color: #7748F8 !important;
    background-color: #7748F8;
  }
  .layui-input:focus, .layui-textarea:focus {
    border-color: #7748F8 !important;
    box-shadow: 0 0 0 3px rgba(129, 22, 183, 0.08);

  }
  .layui-unselect:focus,.layui-input-inline .layui-input:focus{
    box-shadow: none;
  }
  .layui-form-checkbox[lay-skin=primary]:hover>i{
    border-color: #7748F8;
  }
}

.layui-btn{
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  &.btn1{
    background-color: #fff;
    color: #7748F8;
    border-radius: 12px;
  }
  &.btn2{
    background-color: #7748F8;
    color: #fff;
    box-shadow: 0px 10px 30px 1px rgba(97,108,201,0.3);
  }
  &.btn3{
    background-color: #DEBB44;
    color: #fff;
  }
  &.btn4{
    background-color: #E67162;
    color: #fff;
  }
  &.btn5{
    border: 1px solid #7748F8;
    color: #7748F8;
    background-color: #fff;
  }
  &.btn6{
    border: 1px solid #9494AA;
    color: #9494AA;
    background-color: #fff;
  }
  &.btn7{
    border: 1px solid transparent;
    color: #7748F8;
    background-color: transparent;
    padding: 0;
    i{
      font-size: 12px;
    }
    span{
      margin: 0 4px;
    }
  }
  &.btn8{
    border: none;
    color: #7748F8;
    background-color: transparent;
  }
  &.btn9{
    border: 1px solid #9494AA;
    color: #9494AA;
    background-color: #fff;
  }
}
.btn_box{
  display: inline-flex;
  align-items: center;
  margin: 0 12px;
  button{
    margin: 0 6px;
  }
}
.layui-laypage .layui-laypage-curr .layui-laypage-em{
  background-color: #7748F8;
}
.layui-laypage a:hover {
  color: #7748F8;
}


.layui-form.zdy-form{
  .layui-input{
    border: 1px solid #9494A8;
    border-radius: 8px;
  }
  .layui-nav-tree .layui-nav-child dd.layui-this, .layui-nav-tree .layui-nav-child dd.layui-this a, .layui-nav-tree .layui-this, .layui-nav-tree .layui-this>a, .layui-nav-tree .layui-this>a:hover{
    background: #7748F8;
  }
  .layui-laydate .layui-this, .layui-laydate .layui-this>div {
    background-color: #7748F8 !important;
  }
  .layui-form-radio:hover>*, .layui-form-radioed, .layui-form-radioed>i,
  .layui-form-select dl dd.layui-this,
  .layui-layout-admin .layui-logo i,
  .layui-laydate-footer span:hover{
    color: #7748F8;
  }
  .layui-form-onswitch,.layui-form-checked[lay-skin=primary]>i{
    border-color: #7748F8 !important;
    background-color: #7748F8;
  }
  .layui-input:focus, .layui-textarea:focus {
    border-color: #7748F8 !important;
    box-shadow: 0 0 0 3px rgba(129, 22, 183, 0.08);

  }
  .layui-unselect:focus,.layui-input-inline .layui-input:focus{
    box-shadow: none;
  }
  .layui-form-checkbox[lay-skin=primary]:hover>i{
    border-color: #7748F8;
  }
}
.layui-layer{
  border-radius: 24px;
}
.layui-layer-admin i[close]{
  color: #000;
  font-size: 30px;
}
.layui-layer-setwin .layui-layer-close2 {
  background: none;
  right: 0;
  top: 0;
  border: none;
  color: #62687e;
  font-size: 24px;
}
.layui-layer-setwin .layui-layer-close2:hover{
  background: none;
}


.qxjc_popup{
  text-align: center;
  padding: 32px 20px;
  i{
    font-size: 48px;
  }
  .text1{
    font-size: 16px;
    margin: 24px 0 8px;
  }
  .btn_box{
    margin-top: 24px;
  }
}
.layui-menu {
  position: absolute;
  display: none;
  z-index: 9999;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.1);
  min-width: 120px;
}

.tag_item{
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
  margin-right: 4px;
}


// 暗色主题变量
@dark-bg: #1a1a1a;
@dark-card-bg: #2d2d2d;
@dark-text: #e0e0e0;
@dark-border: #444;
@dark-hover: #3a3a3a;
@dark-active: #4a4a4a;
@dark-scrollbar: #555;
@dark-scrollbar-thumb: #777;

// 暗色主题样式
body.dark-theme {
  background: @dark-bg;
  color: @dark-text;
  .layui-table{
    background: none;
  }
  .layui-card-header{
    color: @dark-text;
  }
  .layui-laypage{
    span{
      background: none;
      border: none;
    }
    a{
      background: @dark-bg;
      border-color: @dark-border;
      color: @dark-text;
      &.layui-disabled{
        color: @dark-scrollbar-thumb !important;
      }
    }

  }


  .sidebar {
    background-color: @dark-card-bg;
    color: @dark-text;

    .logo {
      border-bottom-color: @dark-border;
    }

    .menu-container {
      .menu-item, .message-item {
        &:hover {
          background-color: @dark-hover;
        }

        &.active {
          background-color: #6C5DD3;
        }
      }

      .line {
        background: @dark-border;
      }

      &::-webkit-scrollbar-track {
        background: @dark-card-bg;
      }

      &::-webkit-scrollbar-thumb {
        background: @dark-scrollbar-thumb;
      }
    }

    .user-info {
      border-top-color: @dark-border;
    }
  }

  .main-card {
    background: @dark-card-bg;
    color: @dark-text;
  }

  .ybp_card {
    &.v1{
      background: #444253;
    }
    &.v2{
      background: #3f4f4b;
    }
    &.v3{
      background: #313743;
    }
    &.v4{
      background: #4f3e29;
    }
  }

  .qxjc_t4 {
    tr {
      td {
        border-color: @dark-border;
        color: @dark-text;
      }

      &:hover td {
        background: @dark-hover;
      }
    }

    th {
      color: darken(@dark-text, 20%);
    }
    a{
      color: @dark-text;
    }

  }
  .text_color1{
    color: #8d63ff !important;
  }
  .ybp_list {
    .item {
      border-bottom-color: @dark-border;
    }
  }

  .list_search {
    .layui-input-wrap{
      .layui-form-label{
        color: @dark-text;
      }
    }
    .layui-input, .layui-select, .layui-textarea,.layui-input-wrap {
      background: @dark-hover;
      color: @dark-text;
    }
  }

  .toggle-btn i {
    color: @dark-text;
  }

  .right-top {
    color: @dark-text;
    .theme-switch:hover{
      background-color: @dark-hover;
    }
    .iconfont:hover {
      background-color: @dark-hover;
    }
  }
}

// 主题切换按钮样式
.theme-switch {
  z-index: 9999;
  display: flex;
  align-items: center;
  margin-right: 12px;
  padding: 0 12px 0 6px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  &::after{
    content: "";
    width: 1px;
    height: 16px;
    background: #e6e6e6;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
  &:hover{
    background: #efefef;
  }

  .switch-btn {
    width: 40px;
    height: 20px;
    background: #ccc;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    margin: 0 8px;

    &::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: white;
      top: 2px;
      left: 2px;
      transition: all 0.3s;
    }

    &.dark::after {
      left: 22px;
      background: #333;
    }
  }

  .iconfont {
    font-size: 16px;
  }
}
