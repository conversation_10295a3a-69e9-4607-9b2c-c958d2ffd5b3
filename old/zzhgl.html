<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>子账号管理</title>
    <link rel="stylesheet" href="layui/css/layui.css">
    <link rel="stylesheet/less" type="text/css" href="style.less">
    <script src="less.js"></script>
    <script src="layui/layui.js"></script>
    <script src="echarts.min.js"></script>
    <script src="common.js"></script>
</head>
<body>
<div class="layui-layout">
    <div class="header" style="padding: 10px 0">
        <div class="logo">
            <img src="img/logo.jpg" alt="Logo">
            <span>智客AI</span>
        </div>
        <div class="flex align-c">
            <div class="flex-0">
                <div class="toggle-btn" id="toggleBtn">
                    <i class="iconfont">&#xe622;</i>
                </div>
            </div>
            <!--                <div class="flex-0">-->
            <!--                    <img class="head" src="img/logo.jpg">-->
            <!--                </div>-->
            <!--                <div class="w-100 flex align-c">-->
            <!--                    <p class="text2">欢迎回来, 用**称</p>-->
            <!--                    <p class="text1">2025年06月17日(星期二)</p>-->
            <!--                </div>-->
            <!--                <div class="flex-0">-->
            <!--                    <span class="text3">点数余额：0.00</span>-->
            <!--                </div>-->
        </div>
    </div>
    <div class="sidebar" id="sidebar">
        <div class="menu-container">
            <div class="menu-item" data-url="index.html">
                <i class="iconfont">&#xe6d2;</i>
                <span>仪表盘</span>
            </div>
            <div class="menu-item active" data-url="zzhgl.html">
                <i class="iconfont">&#xe612;</i>
                <span>子账号管理</span>
            </div>
            <div class="menu-item" data-url="dpgl.html">
                <i class="iconfont">&#xe6a2;</i>
                <span>店铺管理</span>
            </div>
            <div class="menu-item" data-url="gdmb.html">
                <i class="iconfont">&#xe685;</i>
                <span>工单面板</span>
            </div>
            <div class="menu-item" data-url="shgd.html">
                <i class="iconfont">&#xe685;</i>
                <span>售后工单</span>
            </div>
            <div class="line"></div>
            <div class="color1" style="margin-bottom: 20px">消息</div>
            <div class="menu-item">
                <i class="iconfont">&#xe629;</i>
                <span>收件箱</span>
                <div class="message-count">5</div>
            </div>
            <div class="menu-item">
                <i class="iconfont">&#xe60a;</i>
                <span>通知</span>
                <div class="message-count">5</div>
            </div>
        </div>

        <div class="user-info">
            <div class="user-avatar">张</div>
            <div class="user-details">
                <div class="user-name">张三</div>
                <div class="user-position">管理员</div>
            </div>
        </div>
    </div>
    <div class="main-content" id="layout">
        <div class="body">
            <div class="layui-card main-card">
                <div class="layui-card-body">
                    <div class="layui-form">
                        <div class="list_search">
                            <div class="layui-input-wrap" style="width: auto">
                                <input type="text" placeholder="搜索账号" class="layui-input">
                            </div>
                            <div class="layui-input-wrap" style="width: 220px">
                                <label class="layui-form-label">账号状态</label>
                                <select>
                                    <option value="">请选择</option>
                                    <option value="AAA">选项 A</option>
                                    <option value="BBB">选项 B</option>
                                    <option value="CCC">选项 C</option>
                                </select>
                            </div>
                            <div class="layui-input-wrap" style="width: 220px">
                                <label class="layui-form-label">是否在线</label>
                                <select>
                                    <option value="">请选择</option>
                                    <option value="AAA">选项 A</option>
                                    <option value="BBB">选项 B</option>
                                    <option value="CCC">选项 C</option>
                                </select>
                            </div>
                            <div class="btn_box">
                                <button class="layui-btn btn2">搜索</button>
                                <button class="layui-btn btn5">重置</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="layui-card main-card">
                <div class="layui-card-body">
                    <div class="layui-form">
                        <table class="layui-table qxjc_t4">
                            <colgroup>
                                <col>
                                <col>
                                <col>
                                <col>
                                <col>
                                <col>
                                <col>
                                <col>
                                <col>
                            </colgroup>
                            <thead>
                            <tr>
                                <th>账号</th>
                                <th>会员等级</th>
                                <th>余额</th>
                                <th>会员到期</th>
                                <th>账号状态</th>
                                <th>登陆时间</th>
                                <th>是否在线</th>
                                <th>店铺数量</th>
                                <th>店铺在线数量</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>184762</td>
                                <td>0</td>
                                <td>999.00</td>
                                <td>2025/7/5 21:04:34</td>
                                <td>
                                    <span class="layui-badge">禁用</span>
                                </td>
                                <td>登陆时间</td>
                                <td>离线</td>
                                <td>1</td>
                                <td>0</td>
                            </tr>
                            <tr>
                                <td>184762</td>
                                <td>0</td>
                                <td>999.00</td>
                                <td>2025/7/5 21:04:34</td>
                                <td>
                                    <span class="layui-badge layui-bg-green">正常</span>
                                </td>
                                <td>登陆时间</td>
                                <td>离线</td>
                                <td>1</td>
                                <td>0</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="flex justify-c">
                        <div id="demo-laypage-normal-1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="right-top">
        <div class="theme-switch">
            <div class="flex align-c" id="sunIcon">
                <i class="iconfont sun-icon" style="color: #bd48e5">&#xe62e;</i>暗色
            </div>
            <div class="flex align-c" id="moonIcon" style="display: none;">
                <i class="iconfont moon-icon" style="color: #fffb00">&#xe611;</i>亮色
            </div>
        </div>
        <i class="iconfont">&#xe62b;</i>
        <i class="iconfont">&#xe650;</i>
        <i class="iconfont">&#xe75c;</i>
    </div>
</div>

<ul class="layui-menu layui-dropdown-menu" id="contextMenu">
    <li>
        <div class="layui-menu-body-title" style="color: #7748F8">添加子账号</div>
    </li>
    <li>
        <div class="layui-menu-body-title" id="deleteAccount" style="color: #ff5353">删除子账号</div>
    </li>
    <li>
        <div class="layui-menu-body-title">充值点数</div>
    </li>
    <li>
        <div class="layui-menu-body-title">回收点数</div>
    </li>
    <li>
        <div class="layui-menu-body-title">禁用账户</div>
    </li>
    <li>
        <div class="layui-menu-body-title">复制账号密码</div>
    </li>
    <li>
        <div class="layui-menu-body-title">修改登陆密码</div>
    </li>
    <li>
        <div class="layui-menu-body-title" id="deleteAccount2">设置账号等级</div>
    </li>
</ul>

<div id="kclb_popup3" style="display: none;">
    <div class="qxjc_popup">
        <p class="text1" style="margin-top: 0px">是否确认删除?</p>
        <p class="text_color1">删除后，将无法恢复该操作!</p>
        <div class="btn_box flex justify-c">
            <button class="layui-btn btn5">取消</button>
            <button class="layui-btn btn2">确定删除</button>
        </div>
    </div>
</div>
<div id="kclb_popup2" style="display: none;">
    <div class="qxjc_popup">
        <div class="layui-form zdy-form" lay-filter="" style="margin-top: 20px">
            <div class="layui-form-item">
                <label class="layui-form-label">账号等级</label>
                <div class="layui-input-block">
                    <input style="width: 320px;" type="text" placeholder="请输入"  class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block flex">
                    <div class="btn_box justify-l" style="margin-left: -6px;margin-top: 0">
                        <button class="layui-btn btn2">确定</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


</body>

<script>
    layui.use(['form', 'laypage', 'layer'], function () {
        var $ = layui.$;
        var form = layui.form;
        var laypage = layui.laypage;
        var layer = layui.layer;

        form.render();

        laypage.render({
            elem: 'demo-laypage-normal-1',
            count: 50
        });

        // 菜单项点击事件（特定于此页面的功能）
        $('#deleteAccount').on('click', function() {
            layer.open({
                type: 1,
                title: false,
                area: ['400px', '180px'],
                shade: 0.5,
                shadeClose: false,
                content: $('#kclb_popup3')
            });
        });

        $('#deleteAccount2').on('click', function() {
            layer.open({
                type: 1,
                title: false,
                area: ['500px', '200px'],
                shade: 0.5,
                shadeClose: false,
                content: $('#kclb_popup2')
            });
        });
    });
</script>

</html>
