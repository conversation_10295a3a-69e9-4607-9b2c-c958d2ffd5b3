import{_ as U,r,h as q,w as D,a as B,c as E,b as i,i as e,j as t,k as n,g as H,e as h,l as y,s as F,m as G,t as S,E as C}from"./index-DEMbL7kB.js";const $={class:"page-container"},j={class:"search-section"},I={class:"layui-card main-card"},R={class:"layui-card-body"},K={class:"list_search"},L={class:"table-section"},Q={class:"layui-card main-card table-card"},W={class:"layui-card-body"},X={class:"table-wrapper"},Y={class:"pagination-wrapper"},Z={__name:"SubAccountManage",setup(ee){const s=r({account:"",status:"",online:""}),_=r([{account:"184762",level:0,balance:"999.00",expireTime:"2025/7/5 21:04:34",status:"禁用",loginTime:"2025/7/5 21:04:34",online:"离线",storeCount:1,onlineStoreCount:0},{account:"184763",level:0,balance:"999.00",expireTime:"2025/7/5 21:04:34",status:"正常",loginTime:"2025/7/5 21:04:34",online:"离线",storeCount:1,onlineStoreCount:0},{account:"184764",level:1,balance:"1999.00",expireTime:"2025/8/5 21:04:34",status:"正常",loginTime:"2025/7/5 21:04:34",online:"在线",storeCount:3,onlineStoreCount:2},{account:"184765",level:2,balance:"2999.00",expireTime:"2025/9/5 21:04:34",status:"正常",loginTime:"2025/7/5 21:04:34",online:"在线",storeCount:5,onlineStoreCount:4}]),u=r(1),d=r(10),p=r(100),x=q(()=>{const o=window.innerHeight-300;return Math.max(400,Math.min(600,o))}),k=()=>{console.log("搜索:",s.value),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理搜索按钮",text:JSON.stringify(s.value)})}),C.success("搜索功能待实现")},V=()=>{s.value={account:"",status:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理重置按钮"})}),C.info("已重置搜索条件")},T=o=>{d.value=o,console.log(`每页 ${o} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页大小改变",pageSize:o})})},N=o=>{u.value=o,console.log(`当前页: ${o}`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页按钮",page:o})})},b=()=>{window.table_zizhanghao=_.value,window.table_zizhanghao_currentPage=u.value,window.table_zizhanghao_pageSize=d.value,window.table_zizhanghao_total=p.value};D([_,u,d,p],()=>{b()},{deep:!0});const M=()=>{window.table_zizhanghao&&Array.isArray(window.table_zizhanghao)&&(_.value=window.table_zizhanghao),window.table_zizhanghao_currentPage&&(u.value=window.table_zizhanghao_currentPage),window.table_zizhanghao_pageSize&&(d.value=window.table_zizhanghao_pageSize),window.table_zizhanghao_total&&(p.value=window.table_zizhanghao_total)};return window.updateSubAccountData=M,B(()=>{b()}),(o,a)=>{const A=n("el-input"),g=n("el-col"),w=n("el-option"),m=n("el-select"),v=n("el-icon"),f=n("el-button"),J=n("el-row"),c=n("el-table-column"),z=n("el-tag"),O=n("el-table"),P=n("el-pagination");return H(),E("div",$,[i("div",j,[i("div",I,[a[7]||(a[7]=i("div",{class:"layui-card-header"},"子账号管理",-1)),i("div",R,[i("div",K,[e(J,{gutter:20},{default:t(()=>[e(g,{span:6},{default:t(()=>[e(A,{modelValue:s.value.account,"onUpdate:modelValue":a[0]||(a[0]=l=>s.value.account=l),placeholder:"搜索账号",clearable:""},null,8,["modelValue"])]),_:1}),e(g,{span:6},{default:t(()=>[e(m,{modelValue:s.value.status,"onUpdate:modelValue":a[1]||(a[1]=l=>s.value.status=l),placeholder:"账号状态",clearable:"",style:{width:"100%"}},{default:t(()=>[e(w,{label:"正常",value:"normal"}),e(w,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:6},{default:t(()=>[e(m,{modelValue:s.value.online,"onUpdate:modelValue":a[2]||(a[2]=l=>s.value.online=l),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:t(()=>[e(w,{label:"在线",value:"online"}),e(w,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(g,{span:6},{default:t(()=>[e(f,{type:"primary",onClick:k,class:"btn2"},{default:t(()=>[e(v,null,{default:t(()=>[e(y(F))]),_:1}),a[5]||(a[5]=h(" 搜索 "))]),_:1,__:[5]}),e(f,{onClick:V,class:"btn6"},{default:t(()=>[e(v,null,{default:t(()=>[e(y(G))]),_:1}),a[6]||(a[6]=h(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),i("div",L,[i("div",Q,[i("div",W,[i("div",X,[e(O,{data:_.value,style:{width:"100%"},stripe:"",border:"","max-height":x.value,class:"stable-table"},{default:t(()=>[e(c,{prop:"account",label:"账号","min-width":"120"}),e(c,{prop:"level",label:"会员等级","min-width":"100"}),e(c,{prop:"balance",label:"余额","min-width":"120"}),e(c,{prop:"expireTime",label:"会员到期","min-width":"180"}),e(c,{prop:"status",label:"账号状态","min-width":"100"},{default:t(l=>[e(z,{type:l.row.status==="正常"?"success":"danger",size:"small"},{default:t(()=>[h(S(l.row.status),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"loginTime",label:"登陆时间","min-width":"150"}),e(c,{prop:"online",label:"是否在线","min-width":"100"},{default:t(l=>[e(z,{type:l.row.online==="在线"?"success":"info",size:"small"},{default:t(()=>[h(S(l.row.online),1)]),_:2},1032,["type"])]),_:1}),e(c,{prop:"storeCount",label:"店铺数量","min-width":"100"}),e(c,{prop:"onlineStoreCount",label:"店铺在线数量","min-width":"120"})]),_:1},8,["data","max-height"])]),i("div",Y,[e(P,{"current-page":u.value,"onUpdate:currentPage":a[3]||(a[3]=l=>u.value=l),"page-size":d.value,"onUpdate:pageSize":a[4]||(a[4]=l=>d.value=l),"page-sizes":[10,20,50,100],total:p.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:T,onCurrentChange:N,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])])}}},te=U(Z,[["__scopeId","data-v-db5f3a17"]]);export{te as default};
