import{_ as D,r as m,i as M,w as $,a as U,c as G,b as o,z as e,A as l,B as n,g as H,e as u,J as S,K as E,L as K,t as i,I as h}from"./index-C24etacr.js";const L={class:"page-container"},R={class:"search-section"},Y={class:"layui-card main-card"},j={class:"layui-card-body"},Q={class:"list_search"},W={class:"table-section"},X={class:"layui-card main-card table-card"},Z={class:"layui-card-body"},ee={class:"table-wrapper"},te=["src"],ae=["title"],oe={class:"text-xs text-gray-500 mt-1"},le={class:"ml-2"},se={class:"text-sm"},ne={class:"font-semibold"},ie={class:"font-semibold text-red-600"},de={class:"text-xs text-gray-500 mt-1"},re={class:"text-sm font-medium"},ue={class:"text-sm text-gray-500"},ce={class:"action-buttons"},pe={class:"pagination-wrapper"},_e={__name:"AfterSalesOrder",setup(me){const d=m({productName:"",afterSalesType:"",status:""}),w=m([{platformIcon:"/img/pdd.png",orderNumber:"694292551694",productInfo:{name:"科沃斯新品T80扫地机器人国家补贴扫拖一体人国家补贴",id:"*********",spec:"t80 x1"},amount:{paid:"2999.00",afterSales:"2999.00"},applyTime:"2025-07-12 13:00:00",type:"商品不满意",typeColor:"#FF6868",status:"待处理",reason:"原因内容xxxxxxxxxxxxxxxxxxxx",logistics:{company:"顺丰",number:"SF694294292551",status:"已签收 2025-06-01"}},{platformIcon:"/img/pdd.png",orderNumber:"694292551695",productInfo:{name:"小米扫地机器人S10+",id:"*********",spec:"S10+ x1"},amount:{paid:"1999.00",afterSales:"1999.00"},applyTime:"2025-07-12 14:00:00",type:"质量问题",typeColor:"#FFA968",status:"处理中",reason:"机器人无法正常工作",logistics:{company:"圆通",number:"YT694294292552",status:"运输中"}},{platformIcon:"/img/pdd.png",orderNumber:"694292551696",productInfo:{name:"iPhone 15 Pro Max 256GB 深空黑色",id:"*********",spec:"256GB 深空黑色"},amount:{paid:"9999.00",afterSales:"9999.00"},applyTime:"2025-07-12 15:00:00",type:"商品损坏",typeColor:"#68B7FF",status:"已完成",reason:"收到商品时屏幕已破损",logistics:{company:"申通",number:"ST694294292553",status:"已退回"}}]),c=m(1),p=m(10),g=m(10),k=M(()=>{const s=window.innerHeight-300;return Math.max(400,Math.min(600,s))}),N=()=>{console.log("搜索:",d.value),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单搜索按钮",text:JSON.stringify(d.value)})}),h.success("搜索功能待实现")},C=()=>{d.value={productName:"",afterSalesType:"",status:""},window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单重置按钮"})}),h.info("已重置搜索条件")},z=(s,t)=>{console.log("忽略售后工单:",s,t),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单忽略",key:t.orderNumber,data:t})}),h.success("已忽略该售后工单")},I=(s,t)=>{console.log("查看售后工单详情:",s,t),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单查看详情",key:t.orderNumber,data:t})}),h.info("查看详情功能待实现")},V=s=>{p.value=s,console.log(`每页 ${s} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单分页大小改变",pageSize:s})})},T=s=>{c.value=s,console.log(`当前页: ${s}`),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单分页按钮",page:s})})},y=()=>{window.table_shouhou=w.value,window.table_shouhou_currentPage=c.value,window.table_shouhou_pageSize=p.value,window.table_shouhou_total=g.value};$([w,c,p,g],()=>{y()},{deep:!0});const F=()=>{window.table_shouhou&&Array.isArray(window.table_shouhou)&&(w.value=window.table_shouhou),window.table_shouhou_currentPage&&(c.value=window.table_shouhou_currentPage),window.table_shouhou_pageSize&&(p.value=window.table_shouhou_pageSize),window.table_shouhou_total&&(g.value=window.table_shouhou_total)};return window.updateAfterSalesData=F,U(()=>{y()}),(s,t)=>{const O=n("el-input"),f=n("el-col"),_=n("el-option"),x=n("el-select"),v=n("el-icon"),b=n("el-button"),J=n("el-row"),r=n("el-table-column"),q=n("el-tag"),A=n("el-table"),B=n("el-pagination");return H(),G("div",L,[o("div",R,[o("div",Y,[t[7]||(t[7]=o("div",{class:"layui-card-header"},"售后工单",-1)),o("div",j,[o("div",Q,[e(J,{gutter:20},{default:l(()=>[e(f,{span:6},{default:l(()=>[e(O,{modelValue:d.value.productName,"onUpdate:modelValue":t[0]||(t[0]=a=>d.value.productName=a),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{span:6},{default:l(()=>[e(x,{modelValue:d.value.afterSalesType,"onUpdate:modelValue":t[1]||(t[1]=a=>d.value.afterSalesType=a),placeholder:"售后类型",clearable:"",style:{width:"100%"}},{default:l(()=>[e(_,{label:"商品不满意",value:"unsatisfied"}),e(_,{label:"质量问题",value:"quality"}),e(_,{label:"商品损坏",value:"damage"})]),_:1},8,["modelValue"])]),_:1}),e(f,{span:6},{default:l(()=>[e(x,{modelValue:d.value.status,"onUpdate:modelValue":t[2]||(t[2]=a=>d.value.status=a),placeholder:"售后状态",clearable:"",style:{width:"100%"}},{default:l(()=>[e(_,{label:"待处理",value:"pending"}),e(_,{label:"处理中",value:"processing"}),e(_,{label:"已完成",value:"completed"})]),_:1},8,["modelValue"])]),_:1}),e(f,{span:6},{default:l(()=>[e(b,{type:"primary",onClick:N,class:"btn2"},{default:l(()=>[e(v,null,{default:l(()=>[e(S(E))]),_:1}),t[5]||(t[5]=u(" 搜索 "))]),_:1,__:[5]}),e(b,{onClick:C,class:"btn6"},{default:l(()=>[e(v,null,{default:l(()=>[e(S(K))]),_:1}),t[6]||(t[6]=u(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),o("div",W,[o("div",X,[o("div",Z,[o("div",ee,[e(A,{data:w.value,style:{width:"100%"},stripe:"",border:"","max-height":k.value,class:"stable-table"},{default:l(()=>[e(r,{width:"60"},{default:l(a=>[o("img",{src:a.row.platformIcon,alt:"平台",class:"w-6 h-6"},null,8,te)]),_:1}),e(r,{prop:"orderNumber",label:"售后编号","min-width":"150"}),e(r,{label:"商品信息","min-width":"300"},{default:l(a=>[o("p",{class:"text-sm font-medium line-clamp-2",title:a.row.productInfo.name},i(a.row.productInfo.name),9,ae),o("div",oe,[o("span",null,"ID："+i(a.row.productInfo.id),1),o("span",le,"规格："+i(a.row.productInfo.spec),1)])]),_:1}),e(r,{label:"金额","min-width":"180"},{default:l(a=>[o("div",se,[o("div",null,[t[8]||(t[8]=u("付款金额：")),o("span",ne,"¥"+i(a.row.amount.paid),1)]),o("div",null,[t[9]||(t[9]=u("售后金额：")),o("span",ie,"¥"+i(a.row.amount.afterSales),1)])])]),_:1}),e(r,{prop:"applyTime",label:"售后申请时间","min-width":"180"}),e(r,{label:"售后类型","min-width":"120"},{default:l(a=>[e(q,{color:a.row.typeColor,size:"small",style:{color:"white",border:"none"}},{default:l(()=>[u(i(a.row.type),1)]),_:2},1032,["color"]),o("p",de,i(a.row.status),1)]),_:1}),e(r,{prop:"reason",label:"申请原因","min-width":"200","show-overflow-tooltip":""}),e(r,{label:"快递信息","min-width":"200"},{default:l(a=>[o("p",re,i(a.row.logistics.company)+" "+i(a.row.logistics.number),1),o("p",ue,i(a.row.logistics.status),1)]),_:1}),e(r,{label:"操作",width:"120",fixed:"right"},{default:l(a=>[o("div",ce,[e(b,{size:"small",type:"primary",onClick:P=>z(a.$index,a.row),class:"btn2 action-btn"},{default:l(()=>t[10]||(t[10]=[u(" 忽略 ")])),_:2,__:[10]},1032,["onClick"]),e(b,{size:"small",type:"danger",onClick:P=>I(a.$index,a.row),class:"btn4 action-btn"},{default:l(()=>t[11]||(t[11]=[u(" 查看详情 ")])),_:2,__:[11]},1032,["onClick"])])]),_:1})]),_:1},8,["data","max-height"])]),o("div",pe,[e(B,{"current-page":c.value,"onUpdate:currentPage":t[3]||(t[3]=a=>c.value=a),"page-size":p.value,"onUpdate:pageSize":t[4]||(t[4]=a=>p.value=a),"page-sizes":[10,20,50,100],total:g.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:V,onCurrentChange:T,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])])}}},ge=D(_e,[["__scopeId","data-v-b8c50f51"]]);export{ge as default};
