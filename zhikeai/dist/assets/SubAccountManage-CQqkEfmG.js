import{_ as q,r as v,h as j,i as $,p as K,j as W,k as X,l as Y,m as Q,q as Z,s as ee,v as te,a as O,o as ne,x as A,c as S,y as V,z as e,A as l,F as L,f as ae,B as r,C as oe,b as m,D as le,t as D,E as ie,G as se,T as ce,n as ue,g as h,H as re,I as H,w as de,e as N,J as U,K as _e,L as pe}from"./index-C24etacr.js";const me={key:0,class:"context-menu-divider"},fe={key:1,class:"context-menu-divider"},ge={__name:"ContextMenu",props:{customMenuItems:{type:Array,default:()=>[]}},emits:["menu-click"],setup(J,{expose:d,emit:C}){const f=J,b=C,_=v(!1),g=v(null),M=v(null),i=j({x:0,y:0});let p=null;const R=[{action:"子账号管理_添加子账号",label:"添加子账号",icon:K,type:"add"},{action:"子账号管理_删除子账号",label:"删除子账号",icon:W,type:"danger"},{action:"子账号管理_充值点数",label:"充值点数",icon:X,type:"normal"},{action:"子账号管理_回收点数",label:"回收点数",icon:Y,type:"normal"},{action:"子账号管理_禁用账户",label:"禁用账户",icon:Q,type:"normal"},{action:"子账号管理_复制账号密码",label:"复制账号密码",icon:Z,type:"normal"},{action:"子账号管理_修改登陆密码",label:"修改登陆密码",icon:ee,type:"normal"},{action:"子账号管理_设置账号等级",label:"设置账号等级",icon:te,type:"normal"}],B=$(()=>f.customMenuItems.length>0?f.customMenuItems:R),E=$(()=>({position:"fixed",left:`${i.x}px`,top:`${i.y}px`,zIndex:9999})),I=(o,t,w)=>{p&&clearTimeout(p),_.value&&y(),p=setTimeout(()=>{M.value=w,i.x=o,i.y=t,_.value=!0,ue(()=>{P()})},10)},y=()=>{p&&(clearTimeout(p),p=null),_.value=!1,M.value=null},P=()=>{if(!g.value)return;const t=g.value.getBoundingClientRect(),w=window.innerWidth,k=window.innerHeight,u=10;if(i.x+t.width>w-u){const n=i.x-t.width;n>=u?i.x=n:i.x=w-t.width-u}if(i.y+t.height>k-u){const n=i.y-t.height;n>=u?i.y=n:i.y=k-t.height-u}i.x<u&&(i.x=u),i.y<u&&(i.y=u)},s=async o=>{const t=M.value;if(o==="子账号管理_删除子账号")try{await re.confirm(`确定要删除账号 ${t?.account} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),a(o,t)}catch{H.info("已取消删除")}else a(o,t);y()},a=(o,t)=>{window.g_click&&window.g_click({request:JSON.stringify({action:o,account:t?.account,data:t})}),b("menu-click",{action:o,data:t})},x=o=>{_.value&&g.value&&!g.value.contains(o.target)&&y()},z=o=>{o.key==="Escape"&&_.value&&y()};return O(()=>{document.addEventListener("click",x),document.addEventListener("keydown",z)}),ne(()=>{document.removeEventListener("click",x),document.removeEventListener("keydown",z),p&&(clearTimeout(p),p=null)}),d({show:I,hide:y}),(o,t)=>{const w=r("el-icon"),k=r("el-menu-item"),u=r("el-menu");return h(),A(ce,{to:"body"},[_.value?(h(),S("div",{key:0,ref_key:"menuRef",ref:g,class:"context-menu",style:se(E.value),onClick:t[0]||(t[0]=ie(()=>{},["stop"]))},[e(u,{class:"context-menu-list",onSelect:s},{default:l(()=>[(h(!0),S(L,null,ae(B.value,(n,T)=>(h(),S(L,{key:n.action},[T===1?(h(),S("div",me)):V("",!0),e(k,{index:n.action,class:oe(n.type)},{default:l(()=>[n.icon?(h(),A(w,{key:0},{default:l(()=>[(h(),A(le(n.icon)))]),_:2},1024)):V("",!0),m("span",null,D(n.label),1)]),_:2},1032,["index","class"]),T===3?(h(),S("div",fe)):V("",!0)],64))),128))]),_:1})],4)):V("",!0)])}}},we=q(ge,[["__scopeId","data-v-9538022b"]]),he={class:"page-container"},ve={class:"search-section"},be={class:"layui-card main-card"},ye={class:"layui-card-body"},xe={class:"list_search"},ze={class:"table-section"},ke={class:"layui-card main-card table-card"},Ce={class:"layui-card-body"},Se={class:"table-wrapper"},Me={class:"pagination-wrapper"},Te={__name:"SubAccountManage",setup(J){const d=v({account:"",status:"",online:""}),C=v([{account:"184762",level:0,balance:"999.00",expireTime:"2025/7/5 21:04:34",status:"禁用",loginTime:"2025/7/5 21:04:34",online:"离线",storeCount:1,onlineStoreCount:0},{account:"184763",level:0,balance:"999.00",expireTime:"2025/7/5 21:04:34",status:"正常",loginTime:"2025/7/5 21:04:34",online:"离线",storeCount:1,onlineStoreCount:0},{account:"184764",level:1,balance:"1999.00",expireTime:"2025/8/5 21:04:34",status:"正常",loginTime:"2025/7/5 21:04:34",online:"在线",storeCount:3,onlineStoreCount:2},{account:"184765",level:2,balance:"2999.00",expireTime:"2025/9/5 21:04:34",status:"正常",loginTime:"2025/7/5 21:04:34",online:"在线",storeCount:5,onlineStoreCount:4}]),f=v(1),b=v(10),_=v(100),g=v(null),M=$(()=>{const s=window.innerHeight-300;return Math.max(400,Math.min(600,s))}),i=()=>{console.log("搜索:",d.value),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理搜索按钮",text:JSON.stringify(d.value)})}),H.success("搜索功能待实现")},p=()=>{d.value={account:"",status:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理重置按钮"})}),H.info("已重置搜索条件")},R=s=>{b.value=s,console.log(`每页 ${s} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页大小改变",pageSize:s})})},B=s=>{f.value=s,console.log(`当前页: ${s}`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页按钮",page:s})})},E=(s,a,x)=>{x.preventDefault(),g.value&&g.value.show(x.clientX,x.clientY,s)},I=({action:s,data:a})=>{},y=()=>{window.table_zizhanghao=C.value,window.table_zizhanghao_currentPage=f.value,window.table_zizhanghao_pageSize=b.value,window.table_zizhanghao_total=_.value};de([C,f,b,_],()=>{y()},{deep:!0});const P=()=>{window.table_zizhanghao&&Array.isArray(window.table_zizhanghao)&&(C.value=window.table_zizhanghao),window.table_zizhanghao_currentPage&&(f.value=window.table_zizhanghao_currentPage),window.table_zizhanghao_pageSize&&(b.value=window.table_zizhanghao_pageSize),window.table_zizhanghao_total&&(_.value=window.table_zizhanghao_total)};return window.updateSubAccountData=P,O(()=>{y()}),(s,a)=>{const x=r("el-input"),z=r("el-col"),o=r("el-option"),t=r("el-select"),w=r("el-icon"),k=r("el-button"),u=r("el-row"),n=r("el-table-column"),T=r("el-tag"),F=r("el-table"),G=r("el-pagination");return h(),S("div",he,[m("div",ve,[m("div",be,[a[7]||(a[7]=m("div",{class:"layui-card-header"},"子账号管理",-1)),m("div",ye,[m("div",xe,[e(u,{gutter:20},{default:l(()=>[e(z,{span:6},{default:l(()=>[e(x,{modelValue:d.value.account,"onUpdate:modelValue":a[0]||(a[0]=c=>d.value.account=c),placeholder:"搜索账号",clearable:""},null,8,["modelValue"])]),_:1}),e(z,{span:6},{default:l(()=>[e(t,{modelValue:d.value.status,"onUpdate:modelValue":a[1]||(a[1]=c=>d.value.status=c),placeholder:"账号状态",clearable:"",style:{width:"100%"}},{default:l(()=>[e(o,{label:"正常",value:"normal"}),e(o,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),e(z,{span:6},{default:l(()=>[e(t,{modelValue:d.value.online,"onUpdate:modelValue":a[2]||(a[2]=c=>d.value.online=c),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:l(()=>[e(o,{label:"在线",value:"online"}),e(o,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(z,{span:6},{default:l(()=>[e(k,{type:"primary",onClick:i,class:"btn2"},{default:l(()=>[e(w,null,{default:l(()=>[e(U(_e))]),_:1}),a[5]||(a[5]=N(" 搜索 "))]),_:1,__:[5]}),e(k,{onClick:p,class:"btn6"},{default:l(()=>[e(w,null,{default:l(()=>[e(U(pe))]),_:1}),a[6]||(a[6]=N(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),m("div",ze,[m("div",ke,[m("div",Ce,[m("div",Se,[e(F,{data:C.value,style:{width:"100%"},stripe:"",border:"","max-height":M.value,class:"stable-table",onRowContextmenu:E},{default:l(()=>[e(n,{prop:"account",label:"账号","min-width":"80"}),e(n,{prop:"level",label:"等级","min-width":"60"}),e(n,{prop:"balance",label:"余额","min-width":"70"}),e(n,{prop:"expireTime",label:"到期时间","min-width":"100"}),e(n,{prop:"status",label:"状态","min-width":"60"},{default:l(c=>[e(T,{type:c.row.status==="正常"?"success":"danger",size:"small"},{default:l(()=>[N(D(c.row.status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"loginTime",label:"登陆时间","min-width":"100"}),e(n,{prop:"online",label:"在线","min-width":"50"},{default:l(c=>[e(T,{type:c.row.online==="在线"?"success":"info",size:"small"},{default:l(()=>[N(D(c.row.online),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"storeCount",label:"店铺","min-width":"50"}),e(n,{prop:"onlineStoreCount",label:"在线店铺","min-width":"70"})]),_:1},8,["data","max-height"])]),m("div",Me,[e(G,{"current-page":f.value,"onUpdate:currentPage":a[3]||(a[3]=c=>f.value=c),"page-size":b.value,"onUpdate:pageSize":a[4]||(a[4]=c=>b.value=c),"page-sizes":[10,20,50,100],total:_.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:R,onCurrentChange:B,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])]),e(we,{ref_key:"contextMenuRef",ref:g,onMenuClick:I},null,512)])}}},Ne=q(Te,[["__scopeId","data-v-2cf5ea8a"]]);export{Ne as default};
