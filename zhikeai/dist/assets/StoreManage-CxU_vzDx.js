import{_ as A,r as y,h as j,i as U,M as D,a as F,o as X,x as I,c as T,y as L,z as e,A as s,F as O,f as Y,B as u,C as Q,b as _,D as Z,t as G,E as ee,G as ne,T as te,n as oe,g as x,H as P,I as z,w as ae,e as $,J as q,K as le,L as ie}from"./index-C24etacr.js";const se={key:0,class:"context-menu-divider"},ce={__name:"StoreContextMenu",props:{customMenuItems:{type:Array,default:()=>[]}},emits:["menu-click"],setup(J,{expose:f,emit:k}){const w=J,v=k,r=y(!1),g=y(null),M=y(null),l=j({x:0,y:0});let d=null;const R=[{action:"店铺管理_清空进线次数",label:"清空进线次数",icon:D,type:"normal"},{action:"店铺管理_清空转接次数",label:"清空转接次数",icon:D,type:"normal"}],N=U(()=>w.customMenuItems.length>0?w.customMenuItems:R),B=U(()=>({position:"fixed",left:`${l.x}px`,top:`${l.y}px`,zIndex:9999})),V=(t,n,p)=>{d&&clearTimeout(d),r.value&&h(),d=setTimeout(()=>{M.value=p,l.x=t,l.y=n,r.value=!0,oe(()=>{E()})},10)},h=()=>{d&&(clearTimeout(d),d=null),r.value=!1,M.value=null},E=()=>{if(!g.value)return;const n=g.value.getBoundingClientRect(),p=window.innerWidth,C=window.innerHeight,c=10;if(l.x+n.width>p-c){const a=l.x-n.width;a>=c?l.x=a:l.x=p-n.width-c}if(l.y+n.height>C-c){const a=l.y-n.height;a>=c?l.y=a:l.y=C-n.height-c}l.x<c&&(l.x=c),l.y<c&&(l.y=c)},i=async t=>{const n=M.value;if(t==="店铺管理_删除店铺")try{await P.confirm(`确定要删除店铺 "${n?.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!1}),o(t,n)}catch{z.info("已取消删除")}else if(t==="店铺管理_清空进线次数")try{await P.confirm(`确定要清空店铺 "${n?.name}" 的进线次数吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),o(t,n)}catch{z.info("已取消操作")}else if(t==="店铺管理_清空转接次数")try{await P.confirm(`确定要清空店铺 "${n?.name}" 的转接次数吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),o(t,n)}catch{z.info("已取消操作")}else o(t,n);h()},o=(t,n)=>{if(window.g_click){const p={店铺管理_清空进线次数:"清空进线次数",店铺管理_清空转接次数:"清空转接次数",店铺管理_编辑店铺:"编辑店铺",店铺管理_删除店铺:"删除店铺"};window.g_click({request:JSON.stringify({action:p[t]||t,storeName:n?.name,account:n?.account,data:n})})}v("menu-click",{action:t,data:n})},b=t=>{r.value&&g.value&&!g.value.contains(t.target)&&h()},S=t=>{t.key==="Escape"&&r.value&&h()};return F(()=>{document.addEventListener("click",b),document.addEventListener("keydown",S)}),X(()=>{document.removeEventListener("click",b),document.removeEventListener("keydown",S),d&&(clearTimeout(d),d=null)}),f({show:V,hide:h}),(t,n)=>{const p=u("el-icon"),C=u("el-menu-item"),c=u("el-menu");return x(),I(te,{to:"body"},[r.value?(x(),T("div",{key:0,ref_key:"menuRef",ref:g,class:"context-menu",style:ne(B.value),onClick:n[0]||(n[0]=ee(()=>{},["stop"]))},[e(c,{class:"context-menu-list",onSelect:i},{default:s(()=>[(x(!0),T(O,null,Y(N.value,(a,H)=>(x(),T(O,{key:a.action},[e(C,{index:a.action,class:Q(a.type)},{default:s(()=>[a.icon?(x(),I(p,{key:0},{default:s(()=>[(x(),I(Z(a.icon)))]),_:2},1024)):L("",!0),_("span",null,G(a.label),1)]),_:2},1032,["index","class"]),H===1?(x(),T("div",se)):L("",!0)],64))),128))]),_:1})],4)):L("",!0)])}}},ue=A(ce,[["__scopeId","data-v-f4132a0f"]]),re={class:"page-container"},de={class:"search-section"},pe={class:"layui-card main-card"},_e={class:"layui-card-body"},me={class:"list_search"},fe={class:"table-section"},we={class:"layui-card main-card table-card"},ge={class:"layui-card-body"},ye={class:"table-wrapper"},ve={class:"pagination-wrapper"},he={__name:"StoreManage",setup(J){const f=y({storeName:"",online:""}),k=y([{name:"科沃斯",todayReception:0,pointsConsumed:0,incomingCount:0,transferCount:0,account:"10086",createTime:"2025/7/5 21:04:34",online:"离线"},{name:"小米有品",todayReception:15,pointsConsumed:120,incomingCount:25,transferCount:3,account:"10087",createTime:"2025/7/5 21:04:34",online:"在线"},{name:"华为商城",todayReception:28,pointsConsumed:200,incomingCount:45,transferCount:5,account:"10088",createTime:"2025/7/5 21:04:34",online:"在线"},{name:"京东自营",todayReception:52,pointsConsumed:380,incomingCount:78,transferCount:8,account:"10089",createTime:"2025/7/5 21:04:34",online:"在线"},{name:"天猫旗舰店",todayReception:0,pointsConsumed:0,incomingCount:0,transferCount:0,account:"10090",createTime:"2025/7/5 21:04:34",online:"离线"}]),w=y(1),v=y(10),r=y(10),g=y(null),M=U(()=>{const i=window.innerHeight-300;return Math.max(400,Math.min(600,i))}),l=()=>{console.log("搜索:",f.value),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理搜索按钮",text:JSON.stringify(f.value)})}),z.success("搜索功能待实现")},d=()=>{f.value={storeName:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理重置按钮"})}),z.info("已重置搜索条件")},R=(i,o,b)=>{b.preventDefault(),g.value&&g.value.show(b.clientX,b.clientY,i)},N=({action:i,data:o})=>{},B=i=>{v.value=i,console.log(`每页 ${i} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页大小改变",pageSize:i})})},V=i=>{w.value=i,console.log(`当前页: ${i}`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页按钮",page:i})})},h=()=>{window.table_dianpu=k.value,window.table_dianpu_currentPage=w.value,window.table_dianpu_pageSize=v.value,window.table_dianpu_total=r.value};ae([k,w,v,r],()=>{h()},{deep:!0});const E=()=>{window.table_dianpu&&Array.isArray(window.table_dianpu)&&(k.value=window.table_dianpu),window.table_dianpu_currentPage&&(w.value=window.table_dianpu_currentPage),window.table_dianpu_pageSize&&(v.value=window.table_dianpu_pageSize),window.table_dianpu_total&&(r.value=window.table_dianpu_total)};return window.updateStoreData=E,F(()=>{h()}),(i,o)=>{const b=u("el-input"),S=u("el-col"),t=u("el-option"),n=u("el-select"),p=u("el-icon"),C=u("el-button"),c=u("el-row"),a=u("el-table-column"),H=u("el-tag"),K=u("el-table"),W=u("el-pagination");return x(),T("div",re,[_("div",de,[_("div",pe,[o[6]||(o[6]=_("div",{class:"layui-card-header"},"店铺管理",-1)),_("div",_e,[_("div",me,[e(c,{gutter:20},{default:s(()=>[e(S,{span:8},{default:s(()=>[e(b,{modelValue:f.value.storeName,"onUpdate:modelValue":o[0]||(o[0]=m=>f.value.storeName=m),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),e(S,{span:8},{default:s(()=>[e(n,{modelValue:f.value.online,"onUpdate:modelValue":o[1]||(o[1]=m=>f.value.online=m),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:s(()=>[e(t,{label:"在线",value:"online"}),e(t,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(S,{span:8},{default:s(()=>[e(C,{type:"primary",onClick:l,class:"btn2"},{default:s(()=>[e(p,null,{default:s(()=>[e(q(le))]),_:1}),o[4]||(o[4]=$(" 搜索 "))]),_:1,__:[4]}),e(C,{onClick:d,class:"btn6"},{default:s(()=>[e(p,null,{default:s(()=>[e(q(ie))]),_:1}),o[5]||(o[5]=$(" 重置 "))]),_:1,__:[5]})]),_:1})]),_:1})])])])]),_("div",fe,[_("div",we,[_("div",ge,[_("div",ye,[e(K,{data:k.value,style:{width:"100%"},stripe:"",border:"","max-height":M.value,class:"stable-table",onRowContextmenu:R},{default:s(()=>[e(a,{prop:"name",label:"店铺名称","min-width":"150"}),e(a,{prop:"todayReception",label:"今日接待","min-width":"100"}),e(a,{prop:"pointsConsumed",label:"消耗点数","min-width":"100"}),e(a,{prop:"incomingCount",label:"进线次数","min-width":"100"}),e(a,{prop:"transferCount",label:"转接次数","min-width":"100"}),e(a,{prop:"account",label:"所属账号","min-width":"120"}),e(a,{prop:"createTime",label:"入库时间","min-width":"180"}),e(a,{prop:"online",label:"是否在线","min-width":"100"},{default:s(m=>[e(H,{type:m.row.online==="在线"?"success":"info",size:"small"},{default:s(()=>[$(G(m.row.online),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data","max-height"])]),_("div",ve,[e(W,{"current-page":w.value,"onUpdate:currentPage":o[2]||(o[2]=m=>w.value=m),"page-size":v.value,"onUpdate:pageSize":o[3]||(o[3]=m=>v.value=m),"page-sizes":[10,20,50,100],total:r.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:B,onCurrentChange:V,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])]),e(ue,{ref_key:"contextMenuRef",ref:g,onMenuClick:N},null,512)])}}},xe=A(he,[["__scopeId","data-v-6129ceb9"]]);export{xe as default};
