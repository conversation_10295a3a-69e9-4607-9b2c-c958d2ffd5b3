import{_ as P,r,h as q,w as D,a as U,c as B,b as i,i as e,j as t,k as a,g as E,e as w,l as h,s as H,m as A,t as F,E as y}from"./index-DEMbL7kB.js";const G={class:"page-container"},$={class:"search-section"},j={class:"layui-card main-card"},I={class:"layui-card-body"},K={class:"list_search"},L={class:"table-section"},Q={class:"layui-card main-card table-card"},W={class:"layui-card-body"},X={class:"table-wrapper"},Y={class:"pagination-wrapper"},Z={__name:"StoreManage",setup(ee){const c=r({storeName:"",online:""}),p=r([{name:"科沃斯",todayReception:0,pointsConsumed:0,incomingCount:0,transferCount:0,account:"10086",createTime:"2025/7/5 21:04:34",online:"离线"},{name:"小米有品",todayReception:15,pointsConsumed:120,incomingCount:25,transferCount:3,account:"10087",createTime:"2025/7/5 21:04:34",online:"在线"},{name:"华为商城",todayReception:28,pointsConsumed:200,incomingCount:45,transferCount:5,account:"10088",createTime:"2025/7/5 21:04:34",online:"在线"},{name:"京东自营",todayReception:52,pointsConsumed:380,incomingCount:78,transferCount:8,account:"10089",createTime:"2025/7/5 21:04:34",online:"在线"},{name:"天猫旗舰店",todayReception:0,pointsConsumed:0,incomingCount:0,transferCount:0,account:"10090",createTime:"2025/7/5 21:04:34",online:"离线"}]),d=r(1),u=r(10),_=r(10),C=q(()=>{const o=window.innerHeight-300;return Math.max(400,Math.min(600,o))}),S=()=>{console.log("搜索:",c.value),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理搜索按钮",text:JSON.stringify(c.value)})}),y.success("搜索功能待实现")},x=()=>{c.value={storeName:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理重置按钮"})}),y.info("已重置搜索条件")},k=o=>{u.value=o,console.log(`每页 ${o} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页大小改变",pageSize:o})})},z=o=>{d.value=o,console.log(`当前页: ${o}`),window.g_click&&window.g_click({request:JSON.stringify({action:"店铺管理分页按钮",page:o})})},g=()=>{window.table_dianpu=p.value,window.table_dianpu_currentPage=d.value,window.table_dianpu_pageSize=u.value,window.table_dianpu_total=_.value};D([p,d,u,_],()=>{g()},{deep:!0});const N=()=>{window.table_dianpu&&Array.isArray(window.table_dianpu)&&(p.value=window.table_dianpu),window.table_dianpu_currentPage&&(d.value=window.table_dianpu_currentPage),window.table_dianpu_pageSize&&(u.value=window.table_dianpu_pageSize),window.table_dianpu_total&&(_.value=window.table_dianpu_total)};return window.updateStoreData=N,U(()=>{g()}),(o,n)=>{const V=a("el-input"),m=a("el-col"),b=a("el-option"),M=a("el-select"),f=a("el-icon"),v=a("el-button"),R=a("el-row"),s=a("el-table-column"),T=a("el-tag"),J=a("el-table"),O=a("el-pagination");return E(),B("div",G,[i("div",$,[i("div",j,[n[6]||(n[6]=i("div",{class:"layui-card-header"},"店铺管理",-1)),i("div",I,[i("div",K,[e(R,{gutter:20},{default:t(()=>[e(m,{span:8},{default:t(()=>[e(V,{modelValue:c.value.storeName,"onUpdate:modelValue":n[0]||(n[0]=l=>c.value.storeName=l),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),e(m,{span:8},{default:t(()=>[e(M,{modelValue:c.value.online,"onUpdate:modelValue":n[1]||(n[1]=l=>c.value.online=l),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:t(()=>[e(b,{label:"在线",value:"online"}),e(b,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(m,{span:8},{default:t(()=>[e(v,{type:"primary",onClick:S,class:"btn2"},{default:t(()=>[e(f,null,{default:t(()=>[e(h(H))]),_:1}),n[4]||(n[4]=w(" 搜索 "))]),_:1,__:[4]}),e(v,{onClick:x,class:"btn6"},{default:t(()=>[e(f,null,{default:t(()=>[e(h(A))]),_:1}),n[5]||(n[5]=w(" 重置 "))]),_:1,__:[5]})]),_:1})]),_:1})])])])]),i("div",L,[i("div",Q,[i("div",W,[i("div",X,[e(J,{data:p.value,style:{width:"100%"},stripe:"",border:"","max-height":C.value,class:"stable-table"},{default:t(()=>[e(s,{prop:"name",label:"店铺名称","min-width":"150"}),e(s,{prop:"todayReception",label:"今日接待","min-width":"100"}),e(s,{prop:"pointsConsumed",label:"消耗点数","min-width":"100"}),e(s,{prop:"incomingCount",label:"进线次数","min-width":"100"}),e(s,{prop:"transferCount",label:"转接次数","min-width":"100"}),e(s,{prop:"account",label:"所属账号","min-width":"120"}),e(s,{prop:"createTime",label:"入库时间","min-width":"180"}),e(s,{prop:"online",label:"是否在线","min-width":"100"},{default:t(l=>[e(T,{type:l.row.online==="在线"?"success":"info",size:"small"},{default:t(()=>[w(F(l.row.online),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data","max-height"])]),i("div",Y,[e(O,{"current-page":d.value,"onUpdate:currentPage":n[2]||(n[2]=l=>d.value=l),"page-size":u.value,"onUpdate:pageSize":n[3]||(n[3]=l=>u.value=l),"page-sizes":[10,20,50,100],total:_.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:k,onCurrentChange:z,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])])}}},te=P(Z,[["__scopeId","data-v-73e059d0"]]);export{te as default};
