import{_ as M,r as m,i as U,w as A,a as D,c as G,b as o,z as a,A as n,B as s,g as H,e as u,J as k,K as W,L as E,t as r,I as g}from"./index-C24etacr.js";const K={class:"page-container"},L={class:"search-section"},R={class:"layui-card main-card"},Y={class:"layui-card-body"},j={class:"list_search"},Q={class:"table-section"},X={class:"layui-card main-card table-card"},Z={class:"layui-card-body"},ee={class:"flex items-center mb-2"},te=["src"],ae=["title"],oe={class:"mb-1"},ne={class:"ml-1 text-sm"},le=["title"],se={class:"text-sm font-medium"},ie={class:"text-sm text-gray-500"},re={class:"text-sm font-medium"},de={class:"text-sm text-gray-500"},ce={class:"action-buttons"},ue={class:"pagination-wrapper"},_e={__name:"WorkOrderPanel",setup(me){const i=m({storeName:"",productName:"",eventType:""}),p=m([{eventType:"商品不满意",eventColor:"#FF6868",platformIcon:"/img/pdd.png",storeName:"翼迅科技",productName:"科沃斯新品T80扫地机器人国家补贴扫拖一体人国家补贴",customerName:"我是一只小毛驴",orderNumber:"694292551694292551",trackingNumber:"SF694294292551",logisticsStatus:"已签收 2025-06-01",description:"买家认为商品是假货，买家声称订单有运费险称订单有运费险称订单有运费险称订单有运费险"},{eventType:"超时未发货",eventColor:"#FFA968",platformIcon:"/img/pdd.png",storeName:"翼迅科技",productName:"科沃斯新品T80扫地机器人国家补贴扫拖一体人国家补贴",customerName:"我是一只小毛驴",orderNumber:"694292551694292551",trackingNumber:"SF694294292551",logisticsStatus:"已签收 2025-06-01",description:"买家认为商品是假货，买家声称订单有运费险称订单有运费险称订单有运费险称订单有运费险"},{eventType:"运费补偿",eventColor:"#68B7FF",platformIcon:"/img/pdd.png",storeName:"华为专营店",productName:"iPhone 15 Pro Max 256GB 深空黑色",customerName:"科技达人",orderNumber:"694292551694292552",trackingNumber:"YT694294292552",logisticsStatus:"运输中 2025-07-20",description:"买家要求运费补偿，声称商品描述与实际不符，要求退货退款"},{eventType:"商品不满意",eventColor:"#FF6868",platformIcon:"/img/pdd.png",storeName:"小米官方旗舰店",productName:"小米14 Ultra 16GB+1TB 黑色 徕卡影像",customerName:"摄影爱好者",orderNumber:"694292551694292553",trackingNumber:"SF694294292553",logisticsStatus:"已签收 2025-07-18",description:"买家反馈相机功能不如预期，要求退货处理"}]),d=m(1),c=m(10),w=m(10),S=U(()=>{const l=window.innerHeight-300;return Math.max(400,Math.min(600,l))}),C=()=>{console.log("搜索:",i.value),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板搜索按钮",text:JSON.stringify(i.value)})}),g.success("搜索功能待实现")},T=()=>{i.value={storeName:"",productName:"",eventType:""},window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板重置按钮"})}),g.info("已重置搜索条件")},z=(l,e)=>{console.log("忽略工单:",l,e),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板忽略",key:e.orderNumber,data:e})}),g.success("已忽略该工单")},F=(l,e)=>{console.log("复制工单信息:",l,e),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板复制",key:e.orderNumber,data:e})});const b=`事件类型: ${e.eventType}
商品名称: ${e.productName}
客户昵称: ${e.customerName}
订单号: ${e.orderNumber}`;navigator.clipboard.writeText(b).then(()=>{g.success("工单信息已复制到剪贴板")}).catch(()=>{g.error("复制失败")})},V=l=>{c.value=l,console.log(`每页 ${l} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板分页大小改变",pageSize:l})})},O=l=>{d.value=l,console.log(`当前页: ${l}`),window.g_click&&window.g_click({request:JSON.stringify({action:"工单面板分页按钮",page:l})})},y=()=>{window.table_gongdan=p.value,window.table_gongdan_currentPage=d.value,window.table_gongdan_pageSize=c.value,window.table_gongdan_total=w.value};A([p,d,c,w],()=>{y()},{deep:!0});const $=()=>{window.table_gongdan&&Array.isArray(window.table_gongdan)&&(p.value=window.table_gongdan),window.table_gongdan_currentPage&&(d.value=window.table_gongdan_currentPage),window.table_gongdan_pageSize&&(c.value=window.table_gongdan_pageSize),window.table_gongdan_total&&(w.value=window.table_gongdan_total)};return window.updateWorkOrderData=$,D(()=>{y()}),(l,e)=>{const b=s("el-input"),v=s("el-col"),N=s("el-option"),P=s("el-select"),h=s("el-icon"),f=s("el-button"),B=s("el-row"),x=s("el-tag"),_=s("el-table-column"),I=s("el-table"),J=s("el-pagination");return H(),G("div",K,[o("div",L,[o("div",R,[e[7]||(e[7]=o("div",{class:"layui-card-header"},"工单面板",-1)),o("div",Y,[o("div",j,[a(B,{gutter:20},{default:n(()=>[a(v,{span:6},{default:n(()=>[a(b,{modelValue:i.value.storeName,"onUpdate:modelValue":e[0]||(e[0]=t=>i.value.storeName=t),placeholder:"店铺名称",clearable:""},null,8,["modelValue"])]),_:1}),a(v,{span:6},{default:n(()=>[a(b,{modelValue:i.value.productName,"onUpdate:modelValue":e[1]||(e[1]=t=>i.value.productName=t),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),a(v,{span:6},{default:n(()=>[a(P,{modelValue:i.value.eventType,"onUpdate:modelValue":e[2]||(e[2]=t=>i.value.eventType=t),placeholder:"事件类型",clearable:"",style:{width:"100%"}},{default:n(()=>[a(N,{label:"商品不满意",value:"unsatisfied"}),a(N,{label:"超时未发货",value:"timeout"}),a(N,{label:"运费补偿",value:"shipping"})]),_:1},8,["modelValue"])]),_:1}),a(v,{span:6},{default:n(()=>[a(f,{type:"primary",onClick:C,class:"btn2"},{default:n(()=>[a(h,null,{default:n(()=>[a(k(W))]),_:1}),e[5]||(e[5]=u(" 搜索 "))]),_:1,__:[5]}),a(f,{onClick:T,class:"btn6"},{default:n(()=>[a(h,null,{default:n(()=>[a(k(E))]),_:1}),e[6]||(e[6]=u(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),o("div",Q,[o("div",X,[o("div",Z,[a(I,{data:p.value,style:{width:"100%"},stripe:"",border:"","max-height":S.value,class:"stable-table"},{default:n(()=>[a(_,{label:"事件类型","min-width":"300"},{default:n(t=>[o("div",ee,[o("img",{src:t.row.platformIcon,alt:"平台",class:"w-6 h-6 mr-2"},null,8,te),a(x,{color:t.row.eventColor,size:"small",style:{color:"white",border:"none"}},{default:n(()=>[u(r(t.row.eventType),1)]),_:2},1032,["color"])]),o("p",{class:"text-xs text-gray-500 line-clamp-2",title:t.row.description},r(t.row.description),9,ae)]),_:1}),a(_,{label:"商品名称","min-width":"300"},{default:n(t=>[o("div",oe,[a(x,{type:"primary",size:"small"},{default:n(()=>e[8]||(e[8]=[u("店铺")])),_:1,__:[8]}),o("span",ne,r(t.row.storeName),1)]),o("p",{class:"text-sm text-gray-900 line-clamp-2",title:t.row.productName},r(t.row.productName),9,le)]),_:1}),a(_,{label:"对方昵称/订单号","min-width":"200"},{default:n(t=>[o("p",se,r(t.row.customerName),1),o("p",ie,r(t.row.orderNumber),1)]),_:1}),a(_,{label:"快递单号/物流状态","min-width":"200"},{default:n(t=>[o("p",re,r(t.row.trackingNumber),1),o("p",de,r(t.row.logisticsStatus),1)]),_:1}),a(_,{label:"操作",width:"120",fixed:"right"},{default:n(t=>[o("div",ce,[a(f,{size:"small",type:"primary",onClick:q=>z(t.$index,t.row),class:"btn2 action-btn"},{default:n(()=>e[9]||(e[9]=[u(" 忽略 ")])),_:2,__:[9]},1032,["onClick"]),a(f,{size:"small",type:"danger",onClick:q=>F(t.$index,t.row),class:"btn4 action-btn"},{default:n(()=>e[10]||(e[10]=[u(" 复制 ")])),_:2,__:[10]},1032,["onClick"])])]),_:1})]),_:1},8,["data","max-height"])]),o("div",ue,[a(J,{"current-page":d.value,"onUpdate:currentPage":e[3]||(e[3]=t=>d.value=t),"page-size":c.value,"onUpdate:pageSize":e[4]||(e[4]=t=>c.value=t),"page-sizes":[10,20,50,100],total:w.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:V,onCurrentChange:O,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])}}},pe=M(_e,[["__scopeId","data-v-4c35a1a0"]]);export{pe as default};
